<?php

namespace Modules\HumanResource\Http\Controllers;

use App\ApiBaseMethod;
use App\Attendance;
use App\Center;
use App\CenterEmployee;
use App\CenterTranslation;
use App\LeaveRequest;
use App\Role;
use App\Employee;

use App\Document;
use App\YearCheck;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use DB;
use App\Permission;
use App\EmployeeSalary;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\EmployeeTimetable;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Modules\Admission\Notifications\StudentOfffered;
use Modules\HumanResource\Http\Requests\EmployeeArchiveRequest;
use Modules\HumanResource\Http\Requests\EmployeeStoreRequest;
use Modules\HumanResource\Http\Requests\EmployeeUpdateRequest;
use Modules\Leave\Entities\LeaveType;


class ArchivedEmployeesController extends Controller
{

    public function __construct()
    {


    }

    public function index(Request $request)
    {

        if ($request->ajax()) {

//            dd($request->get("roles"));


            if ($request->filled('roles') or $request->filled('name') or $request->filled('dateRange') or $request->filled('gender') or $request->filled('status')) {


                if ($name = $request->name and isset($request->name)) {
                    $bindings['name'] = $name;
                    $requestedName = '%' . $name . '%';

                    $name_condition = " AND (employees.name LIKE " . "'" . $requestedName . "'" . " OR employees.display_name LIKE  " . "'" . $requestedName . "'" . " OR employees.full_name LIKE  " . "'" . $requestedName . "'" . " OR employees.full_name_trans LIKE " . "'" . $requestedName . "'" . ")";

//                    dd($name_condition);
                }

                if ($status = $request->status == "archived" and $request->filled('status')) {
                    $bindings['status'] = $status;

                    $EmployeesTableStatusqueryPart = " AND employees.deleted_at is not NULL";


                } else {
                    $bindings['status'] = $status;
                    $EmployeesTableStatusqueryPart = " AND employees.deleted_at is NULL";

                }
                if ($roles = $request->get("roles")) {


                    $arr = join(",", $request->roles);
                    $bindings['roles'] = $roles;
                    $roles_condition = " AND roles.id IN (" . $arr . ")";

                }
                if ($registration_date = $request->dateRange and isset($request->dateRange)) {
                    $dateSeperator = explode(",", $request->dateRange);
                    $bindings['registration_date'] = $registration_date;

                    $registration_date_condition = " AND date(employees.created_at) between " . "'" . $dateSeperator[0] . "'" . " AND " . "'" . $dateSeperator[1] . "'";

                }
                if ($gender = $request->gender and isset($request->gender)) {
                    $bindings['gender'] = $gender;
                    $gender_date_condition = " AND employees.gender = " . "'" . $request->gender . "'";
                }

//                dd($requestedName,$request->gender,$dateSeperator[0],$dateSeperator[1]);
//                $trxDatatables = Employee::select()->whereBetween('created_at',[$dateSeperator[0],$dateSeperator[1]])->with('roles')
////                     $q->whereIn('id',$request->get('roles'));
////                })->where(DB::raw('name like'."'$requestedName'"))->where('gender',$request->gender)->select("employees.deleted_at,employees.status,employees.date_of_birth,employees.id,roles.description AS role,employees.name,employees.full_name,employees.full_name_trans,employees.display_name,employees.mobile
////                ->where('gender',$request->gender)->select(['deleted_at','status','date_of_birth','id','name','full_name','full_name_trans','display_name','mobile']);
//                ->where('gender',$request->gender);


//                dd('SELECT roles.description,employees.email,employees.deleted_at,employees.status,employees.date_of_birth,employees.id,employees.name,employees.full_name,employees.full_name_trans,employees.display_name,employees.mobile,employees.created_at,employees.gender FROM employees,roles, model_has_roles
//                    WHERE employees.id = model_has_roles.model_id
//                    AND roles.id = model_has_roles.role_id
//                                            ' . $registration_date_condition . '
//                                            ' . $roles_condition . '
//                                            ' . $EmployeesTableStatusqueryPart . '
//                                            ' . $name_condition . '
//                                            ' . $gender_date_condition.'group by employees.email,roles.description');
                $trxDatatables = DB::select(
                    'SELECT employees.email,employees.deleted_at,employees.status,employees.date_of_birth,employees.id,employees.name,employees.full_name,employees.full_name_trans,employees.display_name,employees.mobile,employees.created_at,employees.gender FROM employees,roles, model_has_roles
                    WHERE employees.id = model_has_roles.model_id
                    AND roles.id = model_has_roles.role_id
                                            ' . $registration_date_condition . '
                                            ' . $roles_condition . '
                                            ' . $EmployeesTableStatusqueryPart . '
                                            ' . $name_condition . '
                                            ' . $gender_date_condition . 'group by employees.email');
//                ,

                return \Yajra\DataTables\DataTables::of($trxDatatables)
                    ->addColumn('role', function ($employee) use ($request) {

                        $roles = '';
                        $colors = ['orange', 'purple', 'yellow', 'green', 'violet', 'pink', 'brown', 'grey', 'black'];

                        $randomColor = Arr::random($colors);
                        // if no roles filter is selected

                        if ($status = $request->status == "archived" and $request->filled('status')) {


                            $employeeWithRoles = Employee::where('id', $employee->id)->with('roles')->withTrashed()->first();

                        } else {

                            $employeeWithRoles = Employee::where('id', $employee->id)->with('roles')->first();

                        }


                        foreach ($employeeWithRoles->roles as $role) {


                            $roles .= '<a class="ui ' . "$randomColor" . ' label">' . $role->description . '</a>&nbsp;';

                        }

//                        }


                        return $roles;

                    })
                    ->addColumn('login', function ($employee) use ($request) {


                        $impersonationRoute = route('multiAuthImpersonate', ['id' => $employee->id, 'guardName' => 'employee']);
                        return '<span class="badge badge-primary badge-pill"><a
                                style="color: white; padding: 0px; background: transparent "
                                href="' . $impersonationRoute . '">login</a></span>';

                    })
                    ->addColumn('status', function ($employee) use ($request) {
//                        $status = '';
//                        if ($request->status == "archived") {
//
//                            $status = 'archived';
//
//                        }else{
//
//
//                            $status =  $employee->status;
//                        }

                        return $employee->status;
                    })
                    ->addColumn('action', function ($row) use ($request) {


                        if (\Auth::user()->can("update employee")) {
                            $stEditRoute = route('employees.edit', $row->id);
                            $viewBtnTitle = "view " . $row->full_name;

                            // different url for archived ( deleted) employees
                            $stShowRoute = is_null($row->deleted_at) == true ?
                                route('employees.show', $row->id)
                                : route('employees.show.archived', $row->id);

                            $viewBtnTitle = "view " . $row->full_name;
                            $editBtnTitle = "edit " . $row->full_name;
                            $archiveBtnTitle = "archive " . $row->full_name;
                            $deleteBtnTitle = "delete " . $row->full_name;
                            $empAttendanceRoute = route('individual.employee.monthly.attendance', $row->id);


                            if ($request->status == "active") {


                                $btns = ' <a href="' . $stShowRoute . '" class="btn btn-success btn-xs" title="' . $viewBtnTitle . '">
                            <span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                              
                               <button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>
                               <a target="_blank" href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true"/></a>';

                            } else {
                                $btns = ' <a href="' . $stShowRoute . '" class="btn btn-success btn-xs" title="' . $viewBtnTitle . '">
                            <span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                             
                            
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>
                               <a target="_blank"  href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true"/></a>';
                                return $btns;

                            }


                            return $btns;
                        }

                        if (\Auth::user()->can("delete employee")) {
                            $stEditRoute = route('employees.edit', $row->id);
                            $stShowRoute = route('employees.show', $row->id);

                            $viewBtnTitle = "view " . $row->full_name;

                            $archiveBtnTitle = "archive " . $row->full_name;
                            $deleteBtnTitle = "delete " . $row->full_name;


                            if ($request->status == "active") {


                                $btns = '<button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';

                            } else {
                                $btns = ' <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                return $btns;

                            }


                            return $btns;
                        }


//                    })->rawColumns(['login','action'])
                    })->
                    rawColumns(['role', 'action', 'login'])
                    ->make(true);


            } else {

                $employeeDatatables = Employee::with("roles")->select();


//                dd($employeeDatatables);

                return \Yajra\DataTables\DataTables::of($employeeDatatables)
                    ->addIndexColumn()
                    ->addColumn('role', function (Employee $employee) {
                        $roles = '';
                        $colors = ['orange', 'purple', 'yellow', 'green', 'violet', 'pink', 'brown', 'grey', 'black'];

                        $randomColor = Arr::random($colors);
                        foreach ($employee->roles as $role) {

                            $roles .= '<a class="ui ' . "$randomColor" . ' label">' . $role->description . '</a>&nbsp;';

                        }
                        return $roles;

                    })
                    ->addColumn('login', function ($row) use ($request) {

                        $impersonationRoute = route('multiAuthImpersonate', ['id' => $row->id, 'guardName' => 'employee']);
                        return '<span class="badge badge-primary badge-pill"><a
                                style="color: white; padding: 0px; background: transparent "
                                href="' . $impersonationRoute . '">login</a></span>';

                    })
                    ->addColumn('action', function ($row) use ($request) {

                        if (\Auth::user()->can("update employee")) {
                            $stEditRoute = route('employees.edit', $row->id);
                            $stShowRoute = route('employees.show', $row->id);

                            $viewBtnTitle = "view " . $row->full_name;
                            $editBtnTitle = "edit " . $row->full_name;
                            $archiveBtnTitle = "archive " . $row->full_name;
                            $deleteBtnTitle = "delete " . $row->full_name;
                            $empAttendanceRoute = route('individual.employee.monthly.attendance', $row->id);

                            if ($row->status == "active") {


                                $btns = ' <a href="' . $stShowRoute . '" class="btn btn-success btn-xs" title="' . $viewBtnTitle . '">
                            <span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                              
                               <button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>
                               <a target="_blank" href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true" /></a>';;

                            } else {
                                $btns = ' <a href="' . $stShowRoute . '" class="btn btn-success btn-xs" title="' . $viewBtnTitle . '">
                            <span class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                             
                            
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>
                               <a target="_blank" href="' . $empAttendanceRoute . '" class="btn btn-primary btn-xs" title="Show Attendance"><span class="glyphicon glyphicon-calendar" aria-hidden="true" /></a>';
                                return $btns;

                            }


                            return $btns;
                        }

                        if (\Auth::user()->can("delete employee")) {
                            $stEditRoute = route('employees.edit', $row->id);
                            $stShowRoute = route('employees.show', $row->id);

                            $viewBtnTitle = "view " . $row->full_name;

                            $archiveBtnTitle = "archive " . $row->full_name;
                            $deleteBtnTitle = "delete " . $row->full_name;


                            if ($request->status == "active") {


                                $btns = '<button  title="' . $archiveBtnTitle . '" class="btn btn-warning btn-xs archiveModalTriggerBtn " id="archiveModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#archive"><span class="glyphicon glyphicon-file"
                               /></button>
                                <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';

                            } else {
                                $btns = ' <button  title="' . $deleteBtnTitle . '" class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "  data-empid=' . $row->id . ' data-toggle="modal"  data-target="#delete"><span class="glyphicon glyphicon-trash"
                               /></button>';
                                return $btns;

                            }


                            return $btns;
                        }


                    })
//                    })->setRowClass(function ($row) {
//
//
//                        return $$row->deleted_at == NULL or $row->deleted_at == null ? '' : 'warning';
//                    })
//                    ->rawColumns(['login','action'])
                    ->rawColumns(['role', 'action', 'login'])
                    ->make(true);
            }

        }


        $result = Employee::latest()->paginate();

        return view('humanresource::archivedEmployees.list', compact('result'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public
    function create()
    {


        $roles = \App\Role::all()->filter(function ($value, $item) {

            return !empty($value->description);
        })->pluck('description', 'name');
//        $roles = Role::pluck('description', 'name');

        return view('humanresource::archivedEmployees.create', compact('roles'));
    }

    public
    function saveUploadDocument(Request $request)
    {

        try {
            if ($request->file('staff_upload_document') != "" && $request->title != "") {
                $document_photo = "";
                if ($request->file('staff_upload_document') != "") {
                    $file = $request->file('staff_upload_document');
//                $document_photo = 'staff-' . md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $document_photo = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                    $file->move('uploads/staff/document/', $document_photo);
                    $document_photo = 'public/uploads/staff/document/' . $document_photo;
                }

                $document = new Document();
                $document->title = $request->title;
                $document->documentable_id = $request->employee_id;
                $document->type = 'stf';
                $document->file = $document_photo;
                $document->created_by = Auth()->user()->id;
                $document->organization_id = config('organization_id');
                $results = $document->save();
            }

            if ($results) {
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            \Log::error($e);
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Response
     */
    public
    function store(EmployeeStoreRequest $request)
    {


        DB::beginTransaction();
        try {


            $resume = "";
            if ($request->file('resume') != "") {

                $file = $request->file('resume');

                $resume = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('uploads/resume/', $resume);
                $resume = 'public/uploads/resume/' . $resume;

            }


            // for upload Employee Joining Letter
            $joining_letter = "";
            if ($request->file('joining_letter') != "") {
                $file = $request->file('joining_letter');
                $joining_letter = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('uploads/staff_joining_letter/', $joining_letter);
                $joining_letter = 'public/uploads/staff_joining_letter/' . $joining_letter;
            }

            // for upload Employee Other Documents
            $other_document = "";
            if ($request->file('other_document') != "") {
                $file = $request->file('other_document');
                $other_document = md5($file->getClientOriginalName() . time()) . "." . $file->getClientOriginalExtension();
                $file->move('uploads/others_documents/', $other_document);
                $other_document = 'public/uploads/others_documents/' . $other_document;
            }


            // Create the employee
            $employee = new Employee;
            $employee->full_name = $request->full_name;
            $employee->name = $request->name;
            $employee->gender = $request->gender;
            $employee->date_of_birth = $request->date_of_birth;
            $employee->email = $request->email;
            $employee->mobile = $request->mobile;
            $employee->organization_id = config('organization_id');
            $employee->password = bcrypt($request->get('password'));
            $employee->nationality = $request->nationality;
            $employee->epf_no = $request->epf_no;
            $employee->status = 'active';
            $employee->contract_type = $request->contract_type;
            $employee->bank_account_name = $request->bank_account_name;
            $employee->bank_account_no = $request->bank_account_no;
            $employee->bank_name = $request->bank_name;
            $employee->resume = $resume;
            $employee->joining_letter = $joining_letter;
            $employee->other_document = $other_document;
            $employee->save();

            $employee->syncRoles($request->roles);

            $employee_salary = EmployeeSalary::create([
                'employee_id' => $employee->id,
                "start_at" => $request->start_at,
                "status" => 'active',
                "work_mood" => $request->work_mood,
                'basic_salary' => $request->basic_salary,
                'hours_per_month' => $request->hours_per_month ?? null,
            ]);
            $days = ['mon', 'tue', 'wed', 'thu', 'fri'];

            foreach ($days as $day) {
                $employee_timetable = EmployeeTimetable::create([
                    'employee_id' => $employee->id,
                    'day' => $day,
                    'clockin' => '09:00:00',
                    'clockout' => '18:00:00',
                    'break' => '0'
                ]);

            }


            DB::commit();

            \Toastr::success('Operation successful', 'Success');


            return redirect()->route('employees.index');
        } catch (\Exception $e) {
            \Log::error($e);
            DB::rollBack();
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }


    }

    public
    function deleteStaffDocumentView(Request $request, $id)
    {


        try {
            $result = Document::where('documentable_id', $id)->first();
            if ($result) {

                if (file_exists($result->file)) {
                    File::delete($result->file);
                }
                $result->delete();
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            \Log::error($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public
    function deleteStaffDocument($id)
    {

        try {
            $result = StudentDocument::where('documentable_id', $id)->first();
            if ($result) {

                if (file_exists($result->file)) {
                    File::delete($result->file);
                }
                $result->delete();
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } else {
                Toastr::error('Operation Failed', 'Failed');
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public
    function showArchivedEmployees(Request $request, Employee $employee)
    {


        if (\Auth::user()->can("update employee")) {


            // decision made on this date that : any new employeee after this date should have only mon-friday working
            if ($employee->created_at < Carbon::parse('2020-12-01')) {

                $newWorkTimeTable = 0;
            } else {
                $newWorkTimeTable = 1;
            }


            $todayAttendance = $this->dailyAttendanceReport($id, $employee);
            $weeklyAttendance = $this->WeeklyAttendanceReport($id, $employee);

            $monthlyAttendance = $this->monthlyAttendanceReport($id, $employee);


        }

        $employeeDocumentsDetails = Document::where('documentable_id', $employee->id)->where('type', '=', 'stf')->get();


        $empAttMonths = DB::select("SELECT DISTINCT (MONTH(clock)) AS months FROM attendances WHERE employee_id = ? ORDER BY months ASC", [$id]);
        $employeeAttYears = DB::select("SELECT DISTINCT (year(clock)) AS years FROM attendances WHERE employee_id = ? ORDER BY years ASC", [$id]);
        $roles = Role::cursor()->pluck('description', 'name');
//        $centers = CenterTranslation::cursor()->where('locale','en')->pluck('name', 'center_id');
        $centers = CenterTranslation::cursor()->where('locale', 'en')->pluck('name', 'name');
        $EmpCenters = CenterTranslation::where('locale', 'en')->whereIn('center_id', CenterEmployee::where('emp_id', $employee->id)->pluck('cen_id')->toArray())->pluck('name', 'name');


        $employeeContainsSupervisorRole = $employee->roles->contains(function ($item, $key) {
            return $item->name == 'supervisor_2_';

        });

//        dd($employee->roles->pluck('name'),$employee->center->pluck('location'));
        $permissions = Permission::all('name', 'id');

        return view('humanresource::archivedEmployees.show', compact('employeeContainsSupervisorRole', 'centers', 'staffDocumentsDetails', 'newWorkTimeTable', 'employee', 'roles', 'permissions', 'empAttMonths', 'employeeAttYears', 'todayAttendance', 'weeklyAttendance', 'monthlyAttendance'));
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public
    function edit($id)
    {
        $employee = Employee::find($id);


        $roles = Role::pluck('description', 'name');

        $permissions = Permission::all('name', 'id');

        return view('humanresource::archivedEmployees.edit', compact('employee', 'roles', 'permissions'));
    }

    public
    function update(EmployeeUpdateRequest $request, Employee $employee)
    {


        try {
            \Illuminate\Support\Facades\DB::beginTransaction();


            $employee->restore();

            // if request has supervisor role, then sync the record in the cen_emps many to many table
            if (collect($request->get('roles'))->contains('supervisor_2_')) {

                $centerIds = CenterTranslation::where('locale', 'en')->whereIn('name', $request->get('centers'))->pluck('center_id')->toArray();

                $employee->center()->sync($centerIds);

            } else {
                $centerIds = CenterEmployee::where('emp_id', auth()->user()->id);

                if ($centerIds->exists()) {
                    $centerIds = $centerIds->pluck('cen_id')->toArray();
                    // if supervisor role is taken from an employee, remove the centers that are assigned to them.
                    $employee->center()->detach($centerIds);
                }


            }
            // check for password change
            if ($request->get('password')) {
                $employee->password = bcrypt($request->get('password'));
            }


            if ($request->get('image')) {
                $employee->image = $request->get('image');
            }
            if ($request->get('employee_number')) {
                $employee->employee_number = $request->get('employee_number');
            }
            if ($request->get('mobile')) {
                $employee->mobile = $request->get('mobile');
            }
            if ($request->get('identity')) {
                $employee->identity = $request->get('identity');
            }
            if ($request->get('identity_number')) {
                $employee->identity_number = $request->get('identity_number');
            }
            if ($request->get('address')) {
                $employee->address = $request->get('address');
            }
            if ($request->get('gender')) {
                $employee->gender = $request->get('gender');
            }
            if ($request->get('marital_status')) {
                $employee->marital_status = $request->get('marital_status');
            }
            if ($request->get('full_name')) {
                $employee->full_name = $request->get('full_name');
            }
            if ($request->get('name')) {
                $employee->name = $request->get('name');
            }

            if ($request->get('email')) {
                $employee->email = $request->get('email');
            }
            if ($request->get('date_of_birth')) {
                $employee->date_of_birth = $request->get('date_of_birth');
            }

            $employee->save();
            // Handle the user roles
            $employee->syncRoles($request->roles);

            \Illuminate\Support\Facades\DB::commit();

            flash()->success(ucfirst($employee->name) . ' has been updated.');
            return redirect()->route('employees.index');
        } catch (\Exception $e) {


            \Illuminate\Support\Facades\DB::rollback();


            \Log::alert($e);


            Toastr::warning($e->getMessage(), 'Danger');
            return redirect()->back();
        }

    }


    public
    function updateSalary(Request $request)
    {
        $salary = $request->all();

        $employee = Employee::findOrFail($request->employee_id);

        if ($employee->current_salary) {
            $current_salary = $employee->current_salary;
            $current_salary->end_at = Carbon::createFromFormat('Y-m-d', $request->start_at)->subDay()->format('Y-m-d');
            $current_salary->save();
        }

        $employee_salary = EmployeeSalary::create([
            'employee_id' => $request->employee_id,
            "start_at" => $request->start_at,
            "work_mood" => $request->work_mood,
            'basic_salary' => $request->basic_salary,
            'hours_per_month' => $request->hours_per_month ?? null,
        ]);

        if ($request->timetable && ($request->flexable != true)) {
            foreach ($request->timetable as $day => $daily) {
                if ($daily && is_array($daily) && $daily != 'off') {
                    $salary_timetable = EmployeeTimetable::create([
                        'employee_salary_id' => $employee_salary->id,
                        'day' => $day,
                        'clockin' => $daily['clockin'],
                        'clockout' => $daily['clockout'],
                        'break' => $daily['break']
                    ]);
                }
            }
        }

        return redirect()->back();

    }

    public
    function destroy($id)
    {
        // if ( Auth::user()->id == $id ) {
        //     flash()->warning('Deletion of currently logged in user is not allowed :(')->important();
        //     return redirect()->back();
        // }


        $employee = Employee::where("id", $id)->forceDelete();
//        \Toastr::success('Employee Deleted', 'Title', ["positionClass" => "toast-top-center"]);

        return response()->json(["message" => 'Employee Deleted !!']);


//        $employee =Employee::findOrFail($id);
//        $name= $employee->name;// show the name of Employee
//
//        $employee->save();
//        $employee->delete();


//            flash()->success('Employee '.$name.' has been deleted');


//        return redirect()->route('employees.index');
    }

    private
    function syncPermissions(Request $request, $user)
    {
        // Get the submitted roles
        $roles = $request->get('roles', []);
        $permissions = $request->get('permissions', []);

        // Get the roles
        $roles = Role::find($roles);

        // check for current role changes
        if (!$user->hasAllRoles($roles)) {
            // reset all direct permissions for user
            $user->permissions()->sync([]);
        } else {
            // handle permissions
            $user->syncPermissions($permissions);
        }

        $user->syncRoles($roles);
        return $user;
    }


    public
    function archiveEmployee(EmployeeArchiveRequest $request, $id)
    {

        try {
            DB::beginTransaction();


            $employee = Employee::find($id)->delete();
            $employee = Employee::withTrashed()->find($id);
            $employee->archive_reason = $request->get('archive_reason');
            $employee->archived_by = \Auth::id();
            $employee->archived_by_ip = request()->ip();
            if ($request->get('archive_reason') == 'other') {
                $employee->archive_other_reason = $request->get('otherReason');
            }
            $employee->status = 'archived';
            $employee->save();

            DB::commit();

            return response()->json("employee deleted");


        } catch (\Exception $e) {
            \Log::error($e);
            DB::rollBack();
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }

    }

    public
    function getEmployeesJsonFormat(Request $request)
    {
        $requestedName = '%' . $request->q . '%';
        $name_condition = "name LIKE " . "'" . $requestedName . "'" . " OR display_name LIKE " . "'" . $requestedName . "'" . " OR full_name LIKE  " . "'" . $requestedName . "'" . " OR full_name_trans LIKE " . "'" . $requestedName . "'";


        $my_query = "select * from employees where " . $name_condition;


        $employee = \DB::select($my_query, array($request->q));
        $totalCounts = \DB::select($my_query, array($request->q));
        $totalCounts = count($totalCounts);

        $searchLang = is_arabic($request->q) == true ? "Arabic" : "English";


        return response()->json(["total_count" => $totalCounts, "incomplete_results" => false, "items" => $employee, 'language' => $searchLang], 200);

    }

    public
    function perDateAttendanceReport($id, $date = null)
    {

        $date = new Carbon($date);


        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $start_date = $date;
        $current_month = $date->format('Y-m-d');


//        $current_month = $date;
        $employee = Employee::findOrFail($id);
        /* NEW */


        $total_required_hours = 0;
        $total_working_hours = 0;


        $day = strtolower($date->format('D'));

        $salary_on_this_date = $employee->salaryOn($date->format('Y-m-d'));


        $record = [];

        $attendance_list = Attendance::where('employee_id', $employee->id)
            ->where(DB::raw('date(clock)'), '=', $date->format('Y-m-d'))
            ->orderBy('clock')
            ->get();


        $attendance_duration = $this->calculateAttendanceDuration($attendance_list);


        $record['day'] = $day;

        $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2);
        $record['attendance'] = $attendance_duration['attendance'];

        if ($salary_on_this_date && $salary_on_this_date->work_mood == 'per_hour') {
            $total_required_hours = $salary_on_this_date->hours_per_month;
        }

        if ($salary_on_this_date && $salary_on_this_date->timetable) {
            $required = $salary_on_this_date->timetable->where('day', $day)->first();


            if ($required) {
                $total_working_hours += $record['worked_hours'];

                if ($salary_on_this_date->work_mood == 'per_month') {
                    // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                    $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
                    $total_required_hours += $required_hours;
                    $record['required_hours'] = $required_hours;
                }

            } else {
                $record['required_hours'] = 'off';
            }
        } elseif ($salary_on_this_date->work_mood == 'per_hour') {
            $total_working_hours += $record['worked_hours'];
        }

        $daily_record[$date->format('Y-m-d')] = $record;
        $date->addDay();


        return $total_working_hours;

        return [
            'current_month' => $current_month,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours
        ];
    }


    public
    function dailyAttendanceReport($id, $employee)
    {


        $date = today()->format("Y-m");
        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $report_date = false;

        if ($date) {
            $report_date = Carbon::createFromFormat('Y-m', $date);
        }
        $start_date = $report_date->format('Y-m') . '-' . today()->day;
        $current_month = Carbon::createFromFormat('Y-m-d', $start_date);

//        $current_month = $date;

        /* NEW */
        $end_date = $current_month->copy();
        $date = $current_month->copy();

        $total_working_minutes = 0;
        $total_required_hours = 0;
        $total_working_hours = 0;


        $day = strtolower($date->format('D'));

        $salary_on_this_date = optional($employee)->salaryOn($date->format('Y-m-d'));


        $record = [];

        $attendance_list = Attendance::where('employee_id', $employee->id)
            ->where(DB::raw('date(clock)'), '=', $date->format('Y-m-d'))
            ->orderBy('clock')
            ->get();


        $attendance_duration = $this->calculateAttendanceDuration($attendance_list);


        $record['day'] = $day;

        $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2);
        $record['attendance'] = $attendance_duration['attendance'];

        if ($salary_on_this_date && $salary_on_this_date->work_mood == 'per_hour') {
            $total_required_hours = $salary_on_this_date->hours_per_month;
        }

        if ($salary_on_this_date && $salary_on_this_date->timetable) {
            $required = $salary_on_this_date->timetable->where('day', $day)->first();


            if ($required) {
                $total_working_hours += $record['worked_hours'];

                if ($salary_on_this_date->work_mood == 'per_month') {
                    // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                    $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
                    $total_required_hours += $required_hours;
                    $record['required_hours'] = $required_hours;
                }

            } else {
                $record['required_hours'] = 'off';
            }
        } elseif ($salary_on_this_date->work_mood == 'per_hour') {
            $total_working_hours += $record['worked_hours'];
        }

        $daily_record[$date->format('Y-m-d')] = $record;
        $date->addDay();


        return $total_working_hours;

        return [
            'current_month' => $current_month,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours
        ];
    }

    public
    function WeeklyAttendanceReport($id, $employee)
    {


        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $report_date = false;


        $now = Carbon::now();
        $startDate = $now->subDays(7)->format('Y-m-d');
        $endDate = today()->format('Y-m-d');
        $current_month = Carbon::createFromFormat('Y-m-d', $startDate);

//        $current_month = $date;

        /* NEW */
        $end_date = $current_month->copy();
        $date = $current_month->copy();

        $total_working_minutes = 0;
        $total_required_hours = 0;
        $total_working_hours = 0;

        $day = strtolower($date->format('D'));

        $salary_on_this_date = optional($employee)->salaryOn($date->format('Y-m-d'));


        $record = [];

        $attendance_list = Attendance::where('employee_id', $employee->id)
            ->where(DB::raw('date(clock)'), '>=', $startDate)
            ->where(DB::raw('date(clock)'), '<=', $endDate)
            ->orderBy('clock')
            ->get();


        $attendance_duration = $this->calculateAttendanceDuration($attendance_list);


        $record['day'] = $day;

        $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2);
        $record['attendance'] = $attendance_duration['attendance'];

        if ($salary_on_this_date && $salary_on_this_date->work_mood == 'per_hour') {
            $total_required_hours = $salary_on_this_date->hours_per_month;
        }

        if ($salary_on_this_date && $salary_on_this_date->timetable) {
            $required = $salary_on_this_date->timetable->where('day', $day)->first();


            if ($required) {
                $total_working_hours += $record['worked_hours'];

                if ($salary_on_this_date->work_mood == 'per_month') {
                    // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                    $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
                    $total_required_hours += $required_hours;
                    $record['required_hours'] = $required_hours;
                }

            } else {
                $record['required_hours'] = 'off';
            }
        } elseif ($salary_on_this_date->work_mood == 'per_hour') {
            $total_working_hours += $record['worked_hours'];
        }

        $daily_record[$date->format('Y-m-d')] = $record;
        $date->addDay();


//        return $total_working_hours;

        return [
            'current_month' => $current_month,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours
        ];
    }

    public
    function monthlyAttendanceReport($id, $employee)
    {

        $now = Carbon::now();
        $startDate = $now->month($now->month)->firstOfMonth()->format('Y-m-d');
        $endDate = today()->format('Y-m-d');
        $current_month = Carbon::createFromFormat('Y-m-d', $startDate);

        /* NEW */
        $end_date = $current_month->copy();
        $date = $current_month->copy();

        $total_working_minutes = 0;
        $total_required_hours = 0;
        $total_working_hours = 0;

        $day = strtolower($date->format('D'));


        $salary_on_this_date = optional($employee)->salaryOn($date->format('Y-m-d'));


        $record = [];

        $attendance_list = Attendance::where('employee_id', $employee->id)
            ->where(DB::raw('date(clock)'), '>=', $startDate)
            ->where(DB::raw('date(clock)'), '<=', $endDate)
            ->orderBy('clock')
            ->get();


        $attendance_duration = $this->calculateAttendanceDuration($attendance_list);


        $record['day'] = $day;

        $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2);
        $record['attendance'] = $attendance_duration['attendance'];

        if ($salary_on_this_date && $salary_on_this_date->work_mood == 'per_hour') {
            $total_required_hours = $salary_on_this_date->hours_per_month;
        }


        if ($salary_on_this_date && $salary_on_this_date->timetable) {
            $required = $salary_on_this_date->timetable->where('day', $day)->first();


            if ($required) {
                $total_working_hours += $record['worked_hours'];

                if ($salary_on_this_date->work_mood == 'per_month') {
                    // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                    $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
                    $total_required_hours += $required_hours;
                    $record['required_hours'] = $required_hours;
                }

            } else {
                $record['required_hours'] = 'off';
            }
        } elseif ($salary_on_this_date->work_mood == 'per_hour') {
            $total_working_hours += $record['worked_hours'];
        }

        $daily_record[$date->format('Y-m-d')] = $record;
        $date->addDay();


//        return $total_working_hours;

        return [
            'current_month' => $current_month,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours
        ];
    }

    private
    function calculateAttendanceDuration($attendance_list)
    {

        $attendance = [];

        $total_working_minutes = 0;

        $counter = 0;
        $steps = 1;


        foreach ($attendance_list as $item) {
            if ($item->type == 'in') {
                $counter++;
            }
            $attendance[$counter][$item->type] = $item;
        }

        foreach ($attendance as $i => $entry) {

            // count hours and minutes
            if (isset($entry['in']) && isset($entry['out'])) {
                $working_minutes = $entry['out']->clock->diffInMinutes($entry['in']->clock);
                $attendance[$i]['duration_in_hours'] = number_format($working_minutes / 60, 2);
                $total_working_minutes += $working_minutes;
            } else {
                $attendance[$i]['duration_in_hours'] = 'Error';
            }
        }


        return ['attendance' => $attendance, 'duration' => $total_working_minutes];
    }
}

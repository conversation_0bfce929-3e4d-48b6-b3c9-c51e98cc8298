{"checkpointing": {"enabled": true}, "mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "DBHub": {"command": "npx", "args": ["@bytebase/dbhub"], "env": {"TRANSPORT": "stdio", "DSN": "mysql://root:admin123@localhost:3306/itqan?sslmode=disable", "READONLY": "false"}}, "chrome-mcp-stdio": {"command": "npx", "args": ["node", "C:\\laragon\\bin\\nodejs\\node-v22\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"]}}}
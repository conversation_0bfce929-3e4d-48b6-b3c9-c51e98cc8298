<?php

return [
    'name' => 'General',
    'facebook' => [
        'page_token' => env('FACEBOOK_PAGE_TOKEN', ''),
        'app_secret' => env('FACEBOOK_APP_SECRET', ''),
        'recipient_id' => env('FACEBOOK_RECIPIENT_ID', ''),
        'version' => env('FACEBOOK_GRAPH_API_VERSION', '4.0'),
    ],
    "units" => [
        "dashboards" => [
            "icon" => "building-o",
            "actions" => [
                "have teacher dashboard",
                "have supervisor dashboard",
                "have finance dashboard",
                "have human_resource dashboard",
                "view approval-awaiting new applications"
            ]
        ],
        "backups" => [
            "icon" => "building-o",
            "actions" => [
                "have students backup",
                "download students backup",
            ]
        ],
        "status" => [
            "icon" => "building-o",
            "actions" => [
                "show system Logs"
            ]
        ],
        "commands" => [
            "icon" => "building-o",
            "actions" => [
                "show comamands interface for admins"
            ]
        ],
        "roles" => [
            "icon" => "key",
            "actions" => [
                "access roles",
                "add role",
                "show role",
                "show role create form",
                "show role edit form",
                "update role",
                "remove role"
            ],
        ],
        "strategies" => [
            "icon" => "bullseye",
            "actions" => [
                "access strategies",
                "add strategy",
                "update strategy",
                "delete strategy",
                "view strategy",
            ]
        ],
        "form_builder" => [
            "icon" => "paper",
            "actions" => [
                "access form_builder",
                "add builder_form",
                "update builder_form",
                "delete builder_form",
                "view builder_form",
            ]
        ],
        "user_verifier" => [
            "icon" => "user",
            "actions" => [
                "access user_verifier",
                "add user_verifier",
                "update user_verifier",
                "delete user_verifier",
                "view user_verifier",
            ]
        ]
    ],
];

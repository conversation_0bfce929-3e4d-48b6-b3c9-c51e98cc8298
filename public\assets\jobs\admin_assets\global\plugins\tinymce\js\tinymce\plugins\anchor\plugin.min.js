!function(){"use strict";var t=tinymce.util.Tools.resolve("tinymce.PluginManager"),e=function(t){return/^[A-Za-z][A-Za-z0-9\-:._]*$/.test(t)},n=function(t){var e=t.selection.getNode();return"A"===e.tagName&&""===t.dom.getAttrib(e,"href")?e.id||e.name:""},o=function(t,e){var n=t.selection.getNode();"A"===n.tagName&&""===t.dom.getAttrib(n,"href")?(n.removeAttribute("name"),n.id=e):(t.focus(),t.selection.collapse(!0),t.execCommand("mceInsertContent",!1,t.dom.createHTML("a",{id:e})))},r=function(t){var r=n(t);t.windowManager.open({title:"Anchor",body:{type:"textbox",name:"id",size:40,label:"Id",value:r},onsubmit:function(n){var r,a,i=n.data.id;r=t,(e(a=i)?(o(r,a),0):(r.windowManager.alert("Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores."),1))&&n.preventDefault()}})},a=function(t){t.addCommand("mceAnchor",function(){r(t)})},i=function(t){return function(e){for(var n=0;n<e.length;n++)(o=e[n]).attr("href")||!o.attr("id")&&!o.attr("name")||o.firstChild||e[n].attr("contenteditable",t);var o}},c=function(t){t.on("PreInit",function(){t.parser.addNodeFilter("a",i("false")),t.serializer.addNodeFilter("a",i(null))})},d=function(t){t.addButton("anchor",{icon:"anchor",tooltip:"Anchor",cmd:"mceAnchor",stateSelector:"a:not([href])"}),t.addMenuItem("anchor",{icon:"anchor",text:"Anchor",context:"insert",cmd:"mceAnchor"})};t.add("anchor",function(t){c(t),a(t),d(t)})}();
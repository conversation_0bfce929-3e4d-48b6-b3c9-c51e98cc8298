<?php

declare(strict_types=1);

namespace Modules\EducationalReports\Exports;

use App\Student;
use App\StudentRevisionPlan;
use App\Classes;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\EducationalReports\Exports\Traits\PagesCalculator;
use Carbon\Carbon;

final class EnhancedMonthlyPlansRevisionSheet implements WithTitle, WithStyles, WithEvents
{
    use PagesCalculator;
    private array $filters;

    public function __construct(array $filters)
    {
        $this->filters = $filters;
    }

    /**
     * Get revision plans data - include ALL students in selected classes, even without plans
     */
    private function getRevisionPlans(): Collection
    {
        $classIds = $this->filters['classIds'] ?? [$this->filters['classId']];
        $year = (int) $this->filters['year'];
        $month = (int) $this->filters['month'];
        $studentIdFilter = $this->filters['studentId'] ?? null;

        $planYearMonth = sprintf('%d-%02d', $year, $month);

        // Prefetch classes and metadata
        $classes = Classes::with(['center', 'programs', 'teachers'])->whereIn('id', $classIds)->get()->keyBy('id');

        // Prefetch all active revision plans for the month for selected classes
        $plans = StudentRevisionPlan::with(['fromSurat', 'toSurat', 'class'])
            ->whereIn('class_id', $classIds)
            ->where('plan_year_and_month', $planYearMonth)
            ->where('status', 'active')
            ->get();

        // Build map: key => "student_id|class_id" => plan
        $planMap = $plans->keyBy(function ($p) {
            return $p->student_id . '|' . $p->class_id;
        });

        // Get all ACTIVE student-class associations from pivot (class_students)
        $studentClassQuery = DB::table('class_students as cs')
            ->join('students as s', 's.id', '=', 'cs.student_id')
            ->whereIn('cs.class_id', $classIds)
            ->whereNull('cs.deleted_at')
            ->where('s.status', 'active')  // Only include active students
            ->whereNull('s.deleted_at')    // Exclude soft-deleted students
            ->select(['cs.class_id', 's.id as student_id', 's.full_name']);

        if (!empty($studentIdFilter)) {
            $ids = is_array($studentIdFilter) ? $studentIdFilter : array_filter(array_map('intval', explode(',', (string) $studentIdFilter)));
            if (!empty($ids)) {
                $studentClassQuery->whereIn('cs.student_id', $ids);
            }
        }

        $studentClassPairs = collect($studentClassQuery->get());

        $results = collect();
        $monthLabel = Carbon::create($year, $month, 1)->format('F Y');

        foreach ($studentClassPairs as $row) {
            $class = $classes->get((int) $row->class_id);
            if (!$class) {
                continue;
            }

            $planKey = $row->student_id . '|' . $row->class_id;
            $plan = $planMap->get($planKey);

            $classProgram = optional($class->programs->first())->title ?? 'N/A';
            $teacherNames = $class->teachers->pluck('full_name')->join(', ');

            if ($plan) {
                $direction = $plan->study_direction ?: $this->determineStudyDirection(
                    (int) $plan->start_from_surat,
                    (int) $plan->to_surat,
                    (int) $plan->start_from_ayat,
                    (int) $plan->to_ayat
                );

                $numberOfPages = $this->calculatePagesRange(
                    $direction,
                    (int) $plan->start_from_surat,
                    (int) $plan->start_from_ayat,
                    (int) $plan->to_surat,
                    (int) $plan->to_ayat
                );

                $results->push([
                    'centre_id' => $class->center->id ?? 'N/A',
                    'centre_name' => $class->center->name ?? 'N/A',
                    'class_id' => (int) $row->class_id,
                    'class_name' => ($class->name ?? $class->class_code) ?? 'N/A',
                    'class_program' => $classProgram,
                    'teacher_name' => $teacherNames ?: 'N/A',
                    'month' => optional($plan->created_at)->format('F Y') ?? $monthLabel,
                    'student_id' => (int) $row->student_id,
                    'student_name' => $row->full_name ?? 'N/A',
                    'from_surah' => optional($plan->fromSurat)->name ?? 'N/A',
                    'from_verse' => $plan->start_from_ayat ?? 'N/A',
                    'to_surah' => optional($plan->toSurat)->name ?? 'N/A',
                    'to_verse' => $plan->to_ayat ?? 'N/A',
                    'no_of_pages' => $numberOfPages,
                    'status' => $plan->status ?? 'N/A',
                    'study_direction' => $plan->study_direction ?? 'N/A',
                ]);
            } else {
                $results->push([
                    'centre_id' => $class->center->id ?? 'N/A',
                    'centre_name' => $class->center->name ?? 'N/A',
                    'class_id' => (int) $row->class_id,
                    'class_name' => ($class->name ?? $class->class_code) ?? 'N/A',
                    'class_program' => $classProgram,
                    'teacher_name' => $teacherNames ?: 'N/A',
                    'month' => $monthLabel,
                    'student_id' => (int) $row->student_id,
                    'student_name' => $row->full_name ?? 'N/A',
                    'from_surah' => 'N/A',
                    'from_verse' => 'N/A',
                    'to_surah' => 'N/A',
                    'to_verse' => 'N/A',
                    'no_of_pages' => 'N/A',
                    'status' => 'N/A',
                    'study_direction' => 'N/A',
                ]);
            }
        }

        return $results;
    }

    // Removed old calculateRevisionPages; using PagesCalculator trait for consistent logic

    /**
     * Get analytics data for executive summary (multi-class aggregation)
     */
    private function getAnalyticsData(): array
    {
        $classIds = $this->filters['classIds'] ?? [$this->filters['classId']];
        $year = $this->filters['year'];
        $month = $this->filters['month'];
        $planYearMonth = sprintf('%d-%02d', $year, $month);

        // Class metrics aggregated across all selected classes
        $classMetrics = DB::selectOne("
            SELECT 
                COUNT(DISTINCT srp.student_id) as total_students_with_plans,
                0 as total_memorization_plans,
                COUNT(DISTINCT CASE WHEN srp.status = 'active' THEN srp.id END) as total_revision_plans,
                ROUND(
                    (COUNT(DISTINCT CASE WHEN srp.status = 'active' THEN srp.id END) / 
                     NULLIF(COUNT(DISTINCT srp.id), 0)) * 100, 1
                ) as plan_approval_rate
            FROM student_revision_plans srp
            WHERE srp.class_id IN (" . implode(',', $classIds) . ")
                AND srp.plan_year_and_month = ?
        ", [$planYearMonth]);

        // Performance metrics from actual reports with proper attendance calculation and student filter
        $studentFilter = !empty($this->filters['studentId']) ? " AND srr.student_id = " . intval($this->filters['studentId']) : "";
        $performanceMetrics = DB::selectOne("
            SELECT 
                COUNT(DISTINCT CASE WHEN ao.title IN ('late', 'on_time') THEN srr.id END) as present_sessions,
                COUNT(DISTINCT srr.id) as total_sessions,
                AVG(CASE WHEN eso.weight IS NOT NULL THEN eso.weight ELSE 0 END) as avg_evaluation_score,
                SUM(COALESCE(srr.pages_revised, 0)) as total_pages_revised
            FROM student_revision_report srr
            JOIN class_students cs ON srr.student_id = cs.student_id AND srr.class_id = cs.class_id
            LEFT JOIN attendance_options ao ON srr.attendance_id = ao.id
            LEFT JOIN evaluation_schema_options eso ON srr.revision_evaluation_id = eso.id
            WHERE srr.class_id IN (" . implode(',', $classIds) . ")
                AND YEAR(srr.created_at) = ?
                AND MONTH(srr.created_at) = ?
                AND cs.deleted_at IS NULL
                {$studentFilter}
        ", [$year, $month]);

        // Teacher metrics aggregated
        $teacherMetrics = DB::selectOne("
            SELECT 
                GROUP_CONCAT(DISTINCT u.full_name SEPARATOR ', ') as primary_teacher,
                COUNT(DISTINCT ct.employee_id) as total_teachers
            FROM classes c
            JOIN class_teachers ct ON c.id = ct.class_id
            JOIN employees u ON ct.employee_id = u.id
            WHERE c.id IN (" . implode(',', $classIds) . ")
        ");

        return [
            'class_metrics' => $classMetrics,
            'performance_metrics' => $performanceMetrics,
            'teacher_metrics' => $teacherMetrics
        ];
    }

    /**
     * Create executive summary for multi-class aggregation
     */
    private function createExecutiveSummary($worksheet, $startRow, $analytics): int
    {
        $classMetrics = $analytics['class_metrics'];
        $performanceMetrics = $analytics['performance_metrics'];
        $teacherMetrics = $analytics['teacher_metrics'];

        // Title
        $worksheet->setCellValue("A{$startRow}", "📊 EXECUTIVE SUMMARY - SUPERVISORY DASHBOARD (Multi-Class Revision Plans)");
        $worksheet->mergeCells("A{$startRow}:O{$startRow}");
        $worksheet->getStyle("A{$startRow}")->applyFromArray([
            'font' => ['bold' => true, 'size' => 14, 'color' => ['rgb' => 'FFFFFF']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '7B1FA2']]
        ]);

        $currentRow = $startRow + 2;

        // Key Performance Indicators for multi-class aggregation
        $kpis = [
            ['Metric', 'Value', 'Status', 'Action Required'],
            ['Total Students with Revision Plans', $classMetrics->total_students_with_plans ?? 0,
             ($classMetrics->total_students_with_plans ?? 0) > 0 ? '✅ Active' : '⚠️ Low',
             ($classMetrics->total_students_with_plans ?? 0) > 0 ? 'Monitor Progress' : 'Create Plans'],
            ['Plan Approval Rate', round((float)($classMetrics->plan_approval_rate ?? 0), 1) . '%',
             ((float)($classMetrics->plan_approval_rate ?? 0)) >= 80 ? '✅ Good' : '🔴 Critical',
             ((float)($classMetrics->plan_approval_rate ?? 0)) >= 80 ? 'Maintain Standards' : 'Review Process'],
            ['Revision Attendance Rate', 
             $performanceMetrics->total_sessions > 0 ? 
                round(((float)($performanceMetrics->present_sessions ?? 0) / (float)$performanceMetrics->total_sessions) * 100, 1) . '%' : '0%',
             ($performanceMetrics->total_sessions > 0 && 
              (((float)($performanceMetrics->present_sessions ?? 0) / (float)$performanceMetrics->total_sessions) * 100) >= 75) ? '✅ Good' : '⚠️ Concern',
             ($performanceMetrics->total_sessions > 0 && 
              (((float)($performanceMetrics->present_sessions ?? 0) / (float)$performanceMetrics->total_sessions) * 100) >= 75) ? 'Monitor' : 'Intervention Needed'],
            ['Avg Revision Performance', round((float)($performanceMetrics->avg_evaluation_score ?? 0), 2),
             ((float)($performanceMetrics->avg_evaluation_score ?? 0)) >= 0.7 ? '✅ Good' : '🔴 Poor',
             ((float)($performanceMetrics->avg_evaluation_score ?? 0)) >= 0.7 ? 'Maintain Quality' : 'Training Required'],
            ['Total Pages Revised (All Classes)', $performanceMetrics->total_pages_revised ?? 0,
             ($performanceMetrics->total_pages_revised ?? 0) > 100 ? '✅ On Track' : '⚠️ Behind',
             ($performanceMetrics->total_pages_revised ?? 0) > 100 ? 'Continue Pace' : 'Accelerate'],
            ['Teaching Staff Coverage', $teacherMetrics->primary_teacher ?? 'N/A', '📋 Info', 'Monitor Performance'],
            ['Total Active Classes', count($this->filters['classIds'] ?? [$this->filters['classId']]), '📊 Info', 'Coordinate Activities'],
        ];

        // Add KPI table
        foreach ($kpis as $rowIndex => $kpi) {
            $row = $currentRow + $rowIndex;
            $worksheet->setCellValue("A{$row}", $kpi[0]);
            $worksheet->setCellValue("D{$row}", $kpi[1]);
            $worksheet->setCellValue("G{$row}", $kpi[2]);
            $worksheet->setCellValue("J{$row}", $kpi[3]);
        }

        // Style KPI table
        $kpiEndRow = $currentRow + count($kpis) - 1;
        $worksheet->getStyle("A{$currentRow}:O{$kpiEndRow}")->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        // Header row styling
        $worksheet->getStyle("A{$currentRow}:O{$currentRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '2E7D32']]
        ]);

        return $kpiEndRow + 2;
    }

    /**
     * Get the sheet title
     */
    public function title(): string
    {
        return 'Revision Plans';
    }

    /**
     * Configure sheet events
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $worksheet = $event->sheet->getDelegate();
                $this->populateSheet($worksheet);
            },
        ];
    }

    /**
     * Populate the sheet with data
     */
    private function populateSheet(Worksheet $worksheet): void
    {
        // Executive summary removed per management request
        $currentRow = 1;

        // Get revision plans data
        $plans = $this->getRevisionPlans();
        
        if ($plans->isEmpty()) {
            $worksheet->setCellValue("A{$currentRow}", "No revision plans found for the selected criteria.");
            return;
        }

        // Headers - reordered with Student ID and Student Name first
        $headers = [
            'Student ID',           // A
            'Student Name',         // B  
            'Centre ID',            // C
            'Centre Name',          // D
            'Class ID',             // E
            'Class Name',           // F (new)
            'Class Program',        // G
            'Teacher Name',         // H
            'Month',                // I
            'From Surah',           // J
            'From Verse',           // K
            'To Surah',             // L
            'To Verse',             // M
            'No of Pages',          // N
            'Status',               // O
            'Study Direction'       // P
        ];

        // Set headers
        foreach ($headers as $index => $header) {
            $col = chr(65 + $index);
            $worksheet->setCellValue("{$col}{$currentRow}", $header);
        }

        // Style headers
        $headerRange = "A{$currentRow}:P{$currentRow}";
        $worksheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'color' => ['rgb' => '7B1FA2']],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        $currentRow++;
        $dataStartRow = $currentRow;

        // Group plans by student for merging (Note: Monthly plans typically have one entry per student)
        $groupedPlans = $plans->groupBy('student_id');
        
        foreach ($groupedPlans as $studentId => $studentPlans) {
            $studentStartRow = $currentRow;
            
            foreach ($studentPlans as $plan) {
                // Populate data with reordered columns
                $worksheet->setCellValue("A{$currentRow}", $plan['student_id']);
                $worksheet->setCellValue("B{$currentRow}", $plan['student_name']);
                $worksheet->setCellValue("C{$currentRow}", $plan['centre_id']);
                $worksheet->setCellValue("D{$currentRow}", $plan['centre_name']);
                $worksheet->setCellValue("E{$currentRow}", $plan['class_id']);
                $worksheet->setCellValue("F{$currentRow}", $plan['class_name'] ?? '');
                $worksheet->setCellValue("G{$currentRow}", $plan['class_program']);
                $worksheet->setCellValue("H{$currentRow}", $plan['teacher_name']);
                $worksheet->setCellValue("I{$currentRow}", $plan['month']);
                $worksheet->setCellValue("J{$currentRow}", $plan['from_surah']);
                $worksheet->setCellValue("K{$currentRow}", $plan['from_verse']);
                $worksheet->setCellValue("L{$currentRow}", $plan['to_surah']);
                $worksheet->setCellValue("M{$currentRow}", $plan['to_verse']);
                $worksheet->setCellValue("N{$currentRow}", $plan['no_of_pages']);
                $worksheet->setCellValue("O{$currentRow}", $plan['status']);
                $worksheet->setCellValue("P{$currentRow}", $plan['study_direction']);
                
                $currentRow++;
            }
            
            // Merge Student ID and Student Name cells for this student (if multiple plans per student)
            $studentEndRow = $currentRow - 1;
            if ($studentEndRow > $studentStartRow) {
                $worksheet->mergeCells("A{$studentStartRow}:A{$studentEndRow}");
                $worksheet->mergeCells("B{$studentStartRow}:B{$studentEndRow}");
                
                // Center align merged cells
                $worksheet->getStyle("A{$studentStartRow}:A{$studentEndRow}")->applyFromArray([
                    'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_CENTER]
                ]);
                $worksheet->getStyle("B{$studentStartRow}:B{$studentEndRow}")->applyFromArray([
                    'alignment' => ['vertical' => Alignment::VERTICAL_CENTER, 'horizontal' => Alignment::HORIZONTAL_LEFT]
                ]);
            }
        }

        // Apply borders to data range
        $dataEndRow = $currentRow - 1;
        $dataRange = "A{$dataStartRow}:P{$dataEndRow}";
        $worksheet->getStyle($dataRange)->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['rgb' => '000000']]
            ]
        ]);

        // Auto-size columns
        foreach (range('A', 'P') as $col) {
            $worksheet->getColumnDimension($col)->setAutoSize(true);
        }
    }

    /**
     * Configure sheet styles
     */
    public function styles(Worksheet $sheet): array
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 14]],
        ];
    }
}
var AppTodo=function(){var t=function(){$(".todo-taskbody-due").datepicker({rtl:App.isRTL(),orientation:"left",autoclose:!0}),$(".todo-taskbody-tags").select2({tags:["Testing","Important","Info","Pending","Completed","Requested","Approved"]})},o=function(){App.getViewPort().width<=992?$(".todo-project-list-content").addClass("collapse"):$(".todo-project-list-content").removeClass("collapse").css("height","auto")};return{init:function(){t(),o(),App.addResizeHandler(function(){o()})}}}();jQuery(document).ready(function(){AppTodo.init()});
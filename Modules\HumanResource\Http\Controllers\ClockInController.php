<?php

namespace Modules\HumanResource\Http\Controllers;

use App\CenterTranslation;
use App\Organization;
use App\Student;
use Doctrine\DBAL\Cache\CacheException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;

use Illuminate\Validation\ValidationException;
use Jenssegers\Agent\Agent;

use App\Employee;

use App\Authorizable;

use App\Attendance;
use Carbon\Carbon;
use App\LeaveRequest;
use App\Role;


class ClockInController extends Controller
{
    // use Authorizable;
    /**
     * Display a listing of the resource.
     * @return Response
     */
    public function index()
    {


//        if (auth()->guard('employee')->user()->hasRole('supervisor_2_'))
//        {
//            dd(auth()->guard('employee')->user()->with('center')->first());
//            auth()->guard('employee')->user()->where('votes', '>', 100);
//        }


        // return ;
        // if (!auth()->user()->hasRole('enterprise') && ! auth()->user()->hasRole('finance-manager_1_')) {
        if (!auth()->user()->can('access attendance')) {
            return redirect('/workplace/humanresource/attendance/' . auth()->user()->id);
            $result = [auth()->user()];
        } else {
            $result = Employee::latest()->paginate();
        }

        return view('humanresource::attendance.list', compact('result'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Response
     */
    public function create()
    {
        $roles = Role::pluck('description', 'name');

        return view('humanresource::employees.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {




        // hash password
        $request->merge(['organization_id' => config('organization_id')]);



        if ($request->ajax()) {
            try {
                \DB::beginTransaction();

                $filePaths = [];
                if ($request->hasFile('filedata')) {
                    $files = $request->file('filedata');
                    $employeeId = \Auth::guard('employee')->user()->id;
                    $storagePath = 'public/clockindocuments/' . $employeeId;
                    foreach ($files as $file) {
                        $filePath = $file->storeAs($storagePath, $file->getClientOriginalName());
                        $filePaths[] = $filePath;
                    }
                }

                $userTimezone = $request->get('userTimezone');
                // Retrieve user input for clock-in time
                $clockInTime = $request->input('clockInTime');
//                $currentTime = Carbon::now('UTC');
                $currentTime = Carbon::now($userTimezone);

                // Check if clockInTime is provided and valid, otherwise use current time
                if (!empty($clockInTime)) {
                    try {




                        // Parse the provided time and combine it with today's date
                        $dateTime = Carbon::createFromFormat('Y-m-d g:i A', Carbon::now()->format('Y-m-d') . ' ' . $clockInTime);

                        if ($dateTime->greaterThan($currentTime)) {
                            throw new \Exception("Clock-in time cannot be in the future.");
                        }

                    } catch (\Exception $e) {
                        // In case of parsing error, log the error and use current datetime
                        \Log::error('Error parsing clock-in time: ' . $e->getMessage());
                        $dateTime = Carbon::now('UTC');
                    }
                } else {
                    // No time provided, use the current datetime
                    $dateTime = Carbon::now();
                    // Adjust the Carbon instance to the user's timezone


                    $dateTime->setTimezone($userTimezone);
                }


                $agent = new Agent();
                $attendance = Attendance::create([
                    'employee_id' => auth()->user()->id,
                    'organization_id' => config('organization_id'),
//                    'clock' => date('Y-m-d H:i:s'),
                    'clock' => $dateTime,
                    'type' => 'in',
                    'location' => 'currently disabled as per requested by HR department',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $request->note,
                    'action_trigerred_from_url' => $request->actionTrigerredFromURL,
                    'files' => json_encode($filePaths),
                    'created_by' => auth()->user()->id,
                ]);

                $lastClockInOut = 'Last clock in ' . $attendance->created_at->diffForHumans();

                \DB::commit();
                $clock = Carbon::createFromFormat('Y-m-d H:i:s', $attendance->clock)
                    ->format('g:i A');
                $clock =   ' <div class="col-md-12  ">
                                Time in :

                                <span class="grn lastClockIn">'.$clock.' <i
                                            class="IC-system S12 gry ML3" data-toggle="tooltip"
                                            data-placement="right" data-toggle="tooltip" title="this is just the last time in."></i></span>

                            </div>';
                return response()->json([
                    'status' => 'success',
                    'nextType' => ucfirst('Out'),
                    'clock' => $clock,
                    'lastClockInOut' => $lastClockInOut
                ], 201);

            } catch (\Exception $exception) {
                \Log::error($exception);
                \DB::rollback();

                return response()->json($exception->getMessage());
            }
        }

        // add record by admin

        return redirect()->route('employees.index');
    }

    public function parseFilesInsideFolder()
    {
        $initialPath = storage_path('app/public/clockindocuments/' . \Auth::guard('employee')->user()->id);
        return array_diff(scandir($initialPath), array('..', '.'));


    }

    public function storePairRecord(Request $request)
    {
        // hash password
        $request->merge(['organization_id' => config('organization_id')]);

        if ($request->ajax()) {

            try {

                if (!auth()->user()->can('add attendance')) {
                    return response()->json(['error' => 'not_autorized'], 300);
                }


                if (!$request->exists('in') && $request->exists('out')) {
                    return response()->json('Please complete the details', 422);
                }


                $inTime = Carbon::parse($request->get('in'));
                $inNote = $request->get('inNote');
                $outNote = $request->get('outNote');

                $outTime = Carbon::parse($request->get('out'));

                $outTimeGreater = $outTime->gt($inTime);
                $outTimeLessThanIn = $outTime->lt($inTime);
                $outTimeEqIn = $outTime->equalTo($inTime);
                $InTimeEqOut = $inTime->equalTo($outTime);


                if ($outTimeLessThanIn == true) {

                    return response()->json('The OUT time can not be less than the IN time', 422);
                }
                if ($outTimeEqIn == true or $InTimeEqOut == true) {

                    return response()->json('The OUT time can not be equal to the IN time', 422);
                }
                if ($outTimeGreater == false) {
                    return response()->json('The OUT time can not be less than the IN time . Please select greater time for the OUT field', 422);
                }


                $agent = new Agent();
                $inAttendance = Attendance::create([
                    'employee_id' => $request->get("employee_id"),
                    'organization_id' => config('organization_id'),
                    'clock' => $inTime,
                    'type' => 'in',
                    'location' => $request->location ?? 'NOT',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $inNote,
                    'created_by' => auth()->user()->id,
                ]);

                $outAttendance = Attendance::create([
                    'employee_id' => $request->get("employee_id"),
                    'organization_id' => config('organization_id'),
                    'clock' => $outTime,
                    'type' => 'out',
                    'location' => $request->location ?? 'NOT',
                    'device' => $agent->device(),
                    'ip' => $request->ip(),
                    'note' => $outNote,
                    'created_by' => auth()->user()->id,
                ]);

            } catch (\Exception $exception) {
                \Log::error($exception);
                $errorMessage = $exception->getMessage();
                return response()->json(compact('errorMessage'));
            }
            return response()->json(['status' => 'success'], 200);
        }
        // add record by admin

        return redirect()->route('employees.index');
    }

    /**
     * Show the specified resource.
     * @return Response
     */
    public function show($id, $date = null)
    {


        if (!auth()->user()->can('access attendance')) {
            // $id = auth()->user()->id;
        }
        $date = request()->get('monthFilter');
        $report = $this->report($id, $date);
//        dd($report);
        // to be refactored
        extract($report);

//        return response()->json($daily_record);

        if (request()->ajax()) {


            return \Yajra\DataTables\DataTables::of($daily_record)
                ->with('totalWorkingHours', '<span style="background: #ff8664 " class="badge badge-info">' . $total_working_hours . ' Hours</span>')
                ->with('totalRequiredWorkingHours', '<span style="background: #57a7ff ; color: #fefefe; " class="badge badge-info">' . $total_required_hours . ' Hours</span>')
                ->addIndexColumn()
                ->addColumn('date', function ($record) use ($daily_record) {


//                    return '<span style="text-transform: capitalize">'.$record['day'] . '<br>' . $record['date'].'</span>';
                    return $record['date'];

                })->addColumn('dayandDate', function ($record) use ($daily_record) {


                    return '<span style="text-transform: capitalize">' . $record['day'] . '<br>' . $record['date'] . '</span>';
//                    return $record['date'];

                })->addColumn('date', function ($record) use ($daily_record) {


                    return $record['date'];

                })
                ->addColumn('insandouts', function ($drecord) use ($daily_record) {


                    $str = '';


                    foreach ($drecord['attendance'] as $attendance) {

                        $str .= '<div class="ui equal width center aligned padded grid" style="height: 37px;">';

                        if (isset($attendance['in'])) {

                            $inNote = $attendance['in']->note;
                            $time = $attendance['in']->clock->format('h:iA');
                            $inNoteStar = $attendance['in']->note == true ? '*' : '';
                            $str .= '  <div class="four wide column attendance-record">
                        <button type="button" class="ui  basic button" title=""  style="    width: 80px;" data-inverted="" data-position="top left"  data-tooltip="' . $inNote . '">
                                                   <small class="label label-success" style="border-radius: 6px;">in</small>' . $time . $inNoteStar . ' 
                                                   <div class="clock-note">
                                                      ' . $inNote . '
                                                   </div>
                                               </button></div>';
                        } else {
                            $str .= '<div class="four wide column attendance-record "><button type="button" class="ui  basic button  " style="    width: 80px;" data-inverted="">
                                                   <small class="label label-warning" style="border-radius: 6px;bottom: -9px;"></small>
                                                   <div class="clock-note">
                                                     
                                                   </div>

                                               </button></div>';
                        }
                        if (isset($attendance['out'])) {
                            $outNote = $attendance['out']->note;
                            $time = $attendance['out']->clock->format('h:iA');
                            $outNoteStar = $attendance['out']->note == true ? '*' : '';

                            $str .= '<div class="four wide column attendance-record ">
                                <button type="button" class="ui  basic button"  title="" data-inverted="" data-position="top left" style="    width: 80px;" data-tooltip="' . $outNote . '">
                                                   <small class="label label-warning" style="border-radius: 6px;bottom: -9px;">out</small>' . $time . $outNoteStar . ' 
                                                   <div class="clock-note">
                                                      ' . $outNote . '
                                                   </div>

                                               </button></div>';
                        } else {
                            $str .= '<div class="four wide column attendance-record "><button type="button" class="ui  basic button  " style="    width: 80px;" data-inverted="" data-position="top left" data-tooltip="employee is either working or forgot to punch out">
                                                   <small class="label label-warning" style="border-radius: 6px;bottom: -9px; color:black">Working / Forgot</small>
                                                   <div class="clock-note">
                                                     
                                                   </div>

                                               </button></div>';
                        }

                        if (isset($attendance['duration_in_hours'])) {

                            $str .= ' <div class="four wide column" style="right: 63px;">
 <a class="ui green label" style="border-radius: 6px;">duration</a>' . $attendance["duration_in_hours"];
                        }

                        $str .= '</div></div></div>';

                    }


                    return $str;


                })
                ->addColumn('action', function ($record) {
//                      <button class="ui primary button" data-attendance ="'.json_encode($record['attendance']).'" data-date="'.$record['date'].'" id="editAttendanceBtn">Edit</button>
                    $deletebtn = '';
                    if (!empty($record['attendance'])) {

                        $deletebtn = '<div class="or"></div></div><button  data-date="' . $record['date'] . '" class="ui negative button deleteModalTriggerBtn" id="deleteModalTriggerBtn" data-catid = ' . $record['attendance'] . ' data-toggle = "modal"
                                                                                                        data-target = "#delete" >Delete</button>';

                    }
                    if (isset($record['required_hours']) && $record['required_hours'] == 'off') {
                        $str = '<div class="ui list">
                      <div class="item">Dassy Off';
                    } else {
                        $rh = $record["required_hours"] ?? '';
                        $str = '<div class="ui list">
                      <div class="item">Required: ' . $rh . '</div>
                      <div class="item">Done: ' . $record["worked_hours"] . '</div>
                     
                    </div></div><br>';
                        if (\Auth::user()->can('update attendance')) {
                            $btns = '<div class="ui buttons">
                      <button class="ui primary button" data-toggle="modal" data-target="#editAttendanceModal"  data-date="' . $record['date'] . '" id="editAttendanceBtn">Edit</button>
                      ' . $deletebtn;

                        }
                    }


//
                    return $str . $btns;
                })
                ->rawColumns(['dayandDate', 'insandouts', 'action', 'totalWorkingHours', 'totalRequiredWorkingHours'])
                ->toJson();

        }


        // // /* END NEW */
//         // salaryOn('2018 - 09 - 01');
        if (!$employee->salaries->count()) {
            // need to create a view
            return 'Employee has no salary!!Add Salary Record first';
        }

        $first_working_month = Carbon::createFromFormat('Y - m - d', $employee->salaries->first()->start_at);

//         $attendance_list = Attendance::where('employee_id',$employee->id)
//                                 ->where('clock' , ' >= ' , $current_month->format('Y - m - d'))
//                                 ->where('clock', ' < ' , $next_month->format('Y - m - d'))
//                                 ->get();

        // // return $attendance_list;

//         $attendance = [];

//         $counter = 0;
//         $steps = 1;

//         foreach($attendance_list as $item){
//             if($item->type == 'in'){
//                 $counter++;
//             }
//             $attendance[$item->clock->format('Y - m - d')][$counter][$item->type] = $item;
//         }
//         $total_working_minutes = 0;

//         foreach ($attendance as $key => $record) {
//             foreach($record as $i => $entry){
//                 // check if it is public holiday

//                 // check if on leave

//                 // count hours and minutes
//                 if(isset($entry['in']) && isset($entry['out'])){
//                     $working_minutes = $entry['out']->clock->diffInMinutes($entry['in']->clock);
//                     $attendance[$key][$i]['duration_in_hours'] = number_format($working_minutes/60 , 2);
//                     $total_working_minutes += $working_minutes;
//                 }else{
//                     $attendance[$key][$i]['duration_in_hours'] = 'Error';
//                 }

//             }
//         }
//         $attendance = (array_merge_recursive($attendance , $on_leave));

//         $total_working_hours = number_format($total_working_minutes/60 , 2);
//         // return $attendance;
//         // $attendance = [];
//         // for ($loop = 0 ; $loop < $attendance->count() ; $loop+=2) {

//         //     $attendance[$attendance[$loop]->clock->format('Y - m - d')][] = $item;
//         // }

//         // return $attendance;

//         // return $current_month->format('Y - m - d');
//         // return $employee;


        return view(
            'humanresource::attendance.report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'next_month',
                'current_month',
                'public_holidays',
                'weekend',
                'first_working_month',
                'total_required_hours',
                'total_working_hours'
            )
        );
    }

    /**
     * Show the form for editing the specified resource.
     * @return Response
     */
    public function edit($id)
    {
        $employee = Employee::find($id);
        $roles = Role::pluck('name', 'id');
        $permissions = Permission::all('name', 'id');

        return view('humanresource::employees . edit', compact('employee', 'roles', 'permissions'));
    }


    public function update(Request $request, $id)
    {


        if (!auth()->user()->can('update attendance')) {
            return response()->json(['error' => 'not_autorized'], 300);
        }


        if (!$request->exists('in') && $request->exists('out')) {
            return response()->json('Please complete the details', 422);
        }


        $inTime = new Carbon($request->get('in'));

        $outTime = new Carbon($request->get('out'));
        $outTimeGreater = $outTime->gt($inTime);
        $outTimeEqIn = $outTime->equalTo($inTime);
        $InTimeEqOut = $inTime->equalTo($outTime);


        if ($outTimeEqIn == true or $InTimeEqOut == true) {

            return response()->json('The OUT time can not be equal to the IN time', 422);
        }
        if ($outTimeGreater == false) {
            return response()->json('The OUT time can not be less than the IN time . Please select greater time for the OUT field', 422);
        }
        // --  validation logic ends here


        if (isset($record['in']['id'])) {
            $aRecord = Attendance::findOrFail($record['in']['id']);

            if ($aRecord->clock != $record['in']['clock']) {
                $aRecord->clock = $record['in']['clock'];
                $aRecord->type = $record['in']['type'];

                $aRecord->note = $aRecord->note . " \n Updated By " . auth()->user()->name;

                $aRecord->save();
            }
        } else {
            $agent = new Agent();


            Attendance::create([
                'employee_id' => $request->employee_id,
                'organization_id' => config('organization_id'),
                'clock' => $record['in']['clock'],
                'type' => $record['in']['type'],
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'note' => 'Manual Entry',
                'created_by' => auth()->user()->id,
            ]);
        }

        if (isset($record['out']['id'])) {
            $aRecord = Attendance::findOrFail($record['out']['id']);

            if ($aRecord->clock != $record['out']['clock']) {
                $aRecord->clock = $record['out']['clock'];
                $aRecord->type = $record['out']['type'];
                $aRecord->note = $aRecord->note . "\n Updated By " . auth()->user()->name;

                $aRecord->save();
            }
        } else {
            $agent = new Agent();

            Attendance::create([
                'employee_id' => $request->employee_id,
                'organization_id' => config('organization_id'),
                'clock' => $record['out']['clock'],
                'type' => $record['out']['type'],
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'note' => 'Manual Entry',
                'created_by' => auth()->user()->id,
            ]);
        }


        return $request->records;

        /*
        $this->validate($request, [
            'name' => 'bail | required | min:2',
            'email' => 'required | email | unique:employees,email,' . $id,
            'roles' => 'required | min:1'
        ]);

        // Get the user
        $user = Employee::findOrFail($id);

        // Update user
        $user->fill($request->except('roles', 'permissions', 'password'));

        // check for password change
        if ($request->get('password')) {
            $user->password = bcrypt($request->get('password'));
        }

        // Handle the user roles
        $this->syncPermissions($request, $user);

        $user->save();
        flash()->success('User has been updated . ');
        return redirect()->route('employees . index'); */
    }


    // per day deletion
    public function destroy(Request $request, $date)
    {


        Attendance::where(\DB::raw("date(clock)"), $date)->where("employee_id", $request->get("employee_id"))->delete();


        return response()->json('attendance deleted', 200);
    }


    // per entry deletion
    public function destroyIndividualRecord(Request $request, $id)
    {


        Attendance::where('id', $id)->delete();


        return response()->json('attendance deleted', 200);
    }

    private function syncPermissions(Request $request, $user)
    {
        // Get the submitted roles
        $roles = $request->get('roles', []);
        $permissions = $request->get('permissions', []);

        // Get the roles
        $roles = Role::find($roles);

        // check for current role changes
        if (!$user->hasAllRoles($roles)) {
            // reset all direct permissions for user
            $user->permissions()->sync([]);
        } else {
            // handle permissions
            $user->syncPermissions($permissions);
        }

        $user->syncRoles($roles);
        return $user;
    }

    public function mobile()
    {
        return view('humanresource::attendance . mobile');
    }


    private function calculateAttendanceDuration($attendance_list)
    {

        $attendance = [];

        $total_working_minutes = 0;

        $counter = 0;
        $steps = 1;


        foreach ($attendance_list as $item) {
            if ($item->type == 'in') {
                $counter++;
            }
            $attendance[$counter][$item->type] = $item;
        }


        foreach ($attendance as $i => $entry) {

            // count hours and minutes
            if (isset($entry['in']) && isset($entry['out'])) {
                $working_minutes = $entry['out']->clock->diffInRealMinutes($entry['in']->clock);

//                $attendance[$i]['duration_in_hours'] = number_format($working_minutes / 60, 2);
                $attendance[$i]['duration_in_hours'] = intdiv($working_minutes, 60) . ':' . ($working_minutes % 60);
                $total_working_minutes += $working_minutes;
            } else {
                $attendance[$i]['duration_in_hours'] = 'Error';
            }
        }


        return ['attendance' => $attendance, 'duration' => $total_working_minutes];
    }

    public function report($id, $date)
    {


        $leave_requests = LeaveRequest::where(['employee_id' => $id, 'status' => 'approved'])->get();

        // return $leave_requests;

        $on_leave = [];

        foreach ($leave_requests as $leave_request) {
            if ($leave_request->from_date->format('Y-m-d') == $leave_request->to_date->format('Y-m-d')) {
                $on_leave[$leave_request->from_date->format('Y-m-d')] = ['allowed_time' => $leave_request['allowed_time']];
            } else {
                for ($i = $leave_request->from_date; $i->format('Y-m-d') <= $leave_request->to_date->format('Y-m-d'); $i->addDay()) {
                    $on_leave[$i->format('Y-m-d')] = ['allowed_time' => $leave_request['allowed_time']];
                }
            }
        }


        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $report_date = false;

        if ($date) {
            $report_date = Carbon::createFromFormat('Y-m', $date);
        }

        if ($report_date !== false) {
            $start_date = $report_date->format('Y-m') . '-01';
        } else {
            $start_date = $date ?? date('Y-m-01');
        }


        $current_month = Carbon::createFromFormat('Y-m-d', $start_date);


        $next_month = $current_month->copy()->addMonth();

        $employee = Employee::findOrFail($id);

        if (!$employee->salaries->count()) {
            // Employee has no salary record
        }
        /* NEW */
        $end_date = $current_month->copy()->addMonth();

        $date = $current_month->copy();

        $daily_record = [];

        $total_working_minutes = 0;
        $total_required_hours = 0;
        $total_working_hours = 0;

        while ($date < $end_date) {
            $day = strtolower($date->format('D'));

            $salary_on_this_date = $employee->salaryOn($date->format('Y-m-d'));

            if (!$salary_on_this_date) {
                $date->addDay();

                continue;
            }
            $record = [];

            $attendance_list = Attendance::where('employee_id', $employee->id)
                ->where('clock', '>=', $date->format('Y-m-d  00:00:00'))
                ->where('clock', '<=', $date->format('Y-m-d 23:59:59'))
                ->orderBy('clock')
                ->get();


            $attendance_duration = $this->calculateAttendanceDuration($attendance_list);

            $record['day'] = $day;

//            $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2);
            $record['worked_hours'] = intdiv($attendance_duration['duration'], 60) . ':' . ($attendance_duration['duration'] % 60);
            $record['attendance'] = $attendance_duration['attendance'];

//            if ($salary_on_this_date && $salary_on_this_date->work_mood == 'per_hour') {
            $total_required_hours = $salary_on_this_date->hours_per_month;

//            }
            if ($salary_on_this_date && $salary_on_this_date->timetable) {
                $required = $salary_on_this_date->timetable->where('day', $day)->first();


                if ($required) {
                    $total_working_hours += $record['worked_hours'];

                    if ($salary_on_this_date->work_mood == 'per_month') {
                        // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                        $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;


                        $total_required_hours += $required_hours;
                        $record['required_hours'] = $required_hours;
//                        $record['required_hours'] = $total_required_hours/20;
                        $record['date'] = $date->format('Y-m-d');
                    }
                } else {
                    $record['required_hours'] = 'off';
                }
            } elseif ($salary_on_this_date->work_mood == 'per_hour') {
                $total_working_hours += $record['worked_hours'];
            } else {
                $total_working_hours += $record['worked_hours'];
            }

            $daily_record[$date->format('Y-m-d')] = $record;
            $date->addDay();
        }


        return [
            'employee' => $employee,
            // 'attendance' => $attendance,
            'daily_record' => $daily_record,
            'next_month' => $next_month,
            'current_month' => $current_month,
            'public_holidays' => $public_holidays,
            'weekend' => $weekend,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours
        ];
    }

    public
    function perDateAttendanceReport($id, $date = null)
    {

        $date = new Carbon($date);


        // temp ;to be dynamicly loaded

        $weekend = ['Sat', 'Sun'];
        $public_holidays = ['2018-01-01', '2018-01-31'];

        // end of temp


        $start_date = $date;
        $current_month = $date->format('Y-m-d');


//        $current_month = $date;
        $employee = Employee::findOrFail($id);
        /* NEW */


        $total_required_hours = 0;
        $total_working_hours = 0;


        $day = strtolower($date->format('D'));

        $salary_on_this_date = $employee->salaryOn($date->format('Y-m-d'));


        $record = [];

        $attendance_list = Attendance::where('employee_id', $employee->id)
            ->where(DB::raw('date(clock)'), '=', $date->format('Y-m-d'))
            ->orderBy('clock')
            ->get();


        $attendance_duration = $this->calculateAttendanceDuration($attendance_list);


        $record['day'] = $day;

        $record['worked_hours'] = number_format($attendance_duration['duration'] / 60, 2);
        $record['attendance'] = $attendance_duration['attendance'];

        if ($salary_on_this_date && $salary_on_this_date->work_mood == 'per_hour') {
            $total_required_hours = $salary_on_this_date->hours_per_month;
        }

        if ($salary_on_this_date && $salary_on_this_date->timetable) {
            $required = $salary_on_this_date->timetable->where('day', $day)->first();


            if ($required) {
                $total_working_hours += $record['worked_hours'];

                if ($salary_on_this_date->work_mood == 'per_month') {
                    // $diff = strtotime("2001-01-01 ".$required->clockin.":00")->diffInMinutes(strtotime("2001-01-01 ".$required->clockout.":00"));

                    $required_hours = Carbon::createFromFormat('H:i:s', $required->clockin)->diffInHours(Carbon::createFromFormat('H:i:s', $required->clockout)) - $required->break;
                    $total_required_hours += $required_hours;
                    $record['required_hours'] = $required_hours;
                }

            } else {
                $record['required_hours'] = 'off';
            }
        } elseif ($salary_on_this_date->work_mood == 'per_hour') {
            $total_working_hours += $record['worked_hours'];
        }

        $daily_record[$date->format('Y-m-d')] = $record;
        $date->addDay();


        return $total_working_hours;

        return [
            'current_month' => $current_month,
            'total_required_hours' => $total_required_hours,
            'total_working_hours' => $total_working_hours
        ];
    }

    public function searche(Request $request)
    {
        if ($request->ajax()) {
            $output = '';
            $query = $request->get('query');

            if ($query != '') {
                $data = Employee::where('name', 'like', '%' . $query . '%')
                    ->orWhere('email', 'like', '%' . $query . '%')
                    ->orWhere('full_name', 'like', '%' . $query . '%')
                    ->orWhere('full_name_trans', 'like', '%' . $query . '%')
                    ->orderBy('name', 'desc')
                    ->get();


            } else {
                $data = Employee::latest()->paginate();

            }
            $total_row = $data->count();


            if ($total_row > 0) {
                foreach ($data as $item) {

                    $output .= '<tr>
            <td> ' . $item->id . '</td>
           <td> ' . $item->name . ' </td>
           <td> ' . $item->roles->implode('description', ', ') . ' </td>
           <td class="text-center" >
               <a href = "' . route('individual.employee.monthly.attendance', $item->id) . '" class="btn btn-sm btn-primary" > View</a >
           </td>
        
           </tr>
                        ';
                }
            } else {
                $output = '
          <tr>
           <td align = "center" colspan = "5" > No Data Found </td>
          </tr>
                        ';
            }


            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );

            echo json_encode($data);
        }
    }

    public function daily_report(Request $request)
    {


        $employee = Employee::all();
        $role = Role::get();

        //  $attendance =Attendance::whereDate('created_at', date('Y - m - d '))->get();

        $attendance = Attendance::whereDate('created_at', date('Y-m-d'))->orderBy('employee_id')->get();


        return view(
            'humanresource::attendance.daily_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date'
            )
        );
    }

    public function daily_report_search(Request $request)
    {


        $id = $request->role;
//        if ($id == '0') {
//            return $this->daily_report($request);
//        }


        $employee = Employee::whereHas('roles', function ($q) use ($id) {
            return $q->where('id', '=', $id);
        })->get();
        $employeeIds = Employee::whereHas('roles', function ($q) use ($id) {
            return $q->where('id', '=', $id);
        })->pluck('id')->toArray();


        $attendance = Attendance::whereDate('created_at', date('Y-m-d'))->whereIn("employee_id", $employeeIds)->orderBy('employee_id')->get();
        $role = Role::get();


        //  $role = Role::where('id','=',$request->role)->get();

        //  $attendance =Attendance::whereDate('created_at', date('Y - m - d '))->get();

        return view(
            'humanresource::attendance.daily_report',
            compact(
                'employee',

                'daily_record',
                'role',
                'date'
            )
        );
    }

    public function monthly_report(Request $request, $date = null)
    {


        // to be refactored

        $employee = Employee::all();
        foreach ($employee as $item) {
            $report = $this->report($item->id, $date);
            extract($report);
            $items[] = $report;
        }


        $role = Role::get();
        $attendance = Attendance:: whereYear('created_at', '=', date('Y'))
            ->whereMonth('created_at', '=', date('m'))->orderBy('employee_id')
            ->get();

        //  $attendance =Attendance::whereDate('created_at', date('Y - m'))->orderBy('employee_id')->get();
        return view(
            'humanresource::attendance.monthly_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date',
                'items'
            )
        );
    }

    public function monthly_report_search(Request $request)
    {
        $date = null;
        $id = $request->role;
        if ($id == '0') {
            return $this->monthly_report($request);
        }


        $employee = Employee::whereHas('roles', function ($q) use ($id) {

            return $q->where('id', $id);
        })->get();


        $role = Role::get();
        foreach ($employee as $item) {
            $report = $this->report($item->id, $date);
            extract($report);
            $items[] = $report;
        }


        $attendance = Attendance:: whereYear('created_at', '=', date('Y'))
            ->whereMonth('created_at', '=', date('m'))->orderBy('employee_id')
            ->get();


        return view(
            'humanresource::attendance.monthly_report',
            compact(
                'employee',
                'attendance',
                'daily_record',
                'role',
                'date',
                'items'
            )
        );


    }

    public function addNoteToAtttendance(Request $request)
    {

        try {


            $validationmessages = [
                'note.max' => 'The :attribute is more than 500 characters. Please limit to 500 characters.',
            ];
            $validation = \Validator::make($request->all(), [
                'note' => 'required|max:500'
            ], $validationmessages);


            $attendance = Attendance::where("id", $request->clockId)->update(['note' => $request->note]);

//        } catch(\Illuminate\Database\QueryException $ex){
        } catch (\Exception $ex) {
            return response()->json($ex->getMessage(), 500);


            // Note any method of class PDOException can be called on $ex.
        }
        return response()->json("note added to the attendance", 200);


    }

    public function updateAttendanceIn(Request $request)
    {

        try {

            if (!auth()->user()->can('update attendance')) {
                return response()->json(['error' => 'not_autorized'], 300);
            }


            if (!$request->exists('in') && !$request->exists('out')) {
                return response()->json('Please complete the details', 422);
            }


            $inTime = Carbon::parse($request->get('in'));
            $inNote = $request->get('inNote');

            $agent = new Agent();
            $inAttendance = Attendance::updateOrInsert(['id' => $request->inId], [
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),

                'type' => 'in',
                'employee_id' => $request->employee_id,
                'clock' => $inTime,
                'note' => $inNote,

                'organization_id' => \Config::get('organization_id'),
                'created_by' => auth()->user()->id,
                'updated_by' => auth()->user()->id,
            ]);

//        } catch(\Illuminate\Database\QueryException $ex){
        } catch (\Exception $ex) {
            return response()->json($ex->getMessage(), 500);


            // Note any method of class PDOException can be called on $ex.
        }
        return response()->json("note added to the attendance", 200);


    }

    public function updateAttendanceOut(Request $request)
    {

        try {

            if (!auth()->user()->can('update attendance')) {
                return response()->json(['error' => 'not_autorized'], 300);
            }


            if (!$request->exists('in') && !$request->exists('out')) {
                return response()->json('Please complete the details', 422);
            }


            $outNote = $request->get('outNote');

            $outTime = Carbon::parse($request->get('out'));

            $outTimeGreater = $outTime->gt($inTime);
            $outTimeEqIn = $outTime->equalTo($inTime);


            $agent = new Agent();


            $outAttendance = Attendance::updateOrInsert(['id' => $request->outId], [
                'location' => $request->location ?? 'NOT',
                'device' => $agent->device(),
                'ip' => $request->ip(),
                'type' => 'out',
                'employee_id' => $request->employee_id,
                'clock' => $outTime,
                'note' => $outNote,
                'created_by' => auth()->user()->id,
                'organization_id' => \Config::get('organization_id'),
                'updated_by' => auth()->user()->id,
            ]);

//        } catch(\Illuminate\Database\QueryException $ex){
        } catch (\Exception $ex) {
            return response()->json($ex->getMessage(), 500);


            // Note any method of class PDOException can be called on $ex.
        }
        return response()->json("note added to the attendance", 200);


    }

    public function getAttendanceNote($id)
    {


        $note = Attendance::where("id", $id)->first()->note;


        return response()->json($note);

    }

    public function getAttendancePair($employeeId, $id, $type)
    {

        $result = [];
        if ($type == 'in') {

            $attDate = Attendance::where("id", $id)->select(\DB::raw("date(clock) as date"))->first()->date;


            $nextRow = Attendance::where("employee_id", $employeeId)->where("id", '>', $id)->where(\DB::raw("date(clock)"), $attDate)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();


            if (is_null($nextRow)) {

                $nextDate = new Carbon($nextRow->date);
                $currentRow = Attendance::where("id", $id)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();
                $currentDate = new Carbon($currentRow->date);
                $currentTime = new Carbon($currentRow->time);

                if ($nextDate->gt($currentDate)) {

                    $attendance = Attendance::where("id", $id)->select(\DB::raw('time(clock) attTime,clock,type,employee_id,id,note'))->first()->toArray();
                    $systemGeneratedRecord = ['attTime' => $currentTime->addSeconds(44)->toTimeString(), 'clock' => $currentDate->toDateString() . ' ' . $currentTime->addSeconds(44)->toTimeString(), 'employee_id' => $currentRow->employee_id, 'id' => '', 'note' => '', 'type' => 'out'];

                    $result[] = $attendance;
                    $result[] = $systemGeneratedRecord;


                }
            } else {
                $result = Attendance::where(\DB::raw("date(clock)"), $attDate)->whereIn("id", [$id, $nextRow->id])->select(\DB::raw('time(clock) attTime,time(clock) as time,clock,type,employee_id,id,note'))->get();


            }


        } else {


            $attDate = Attendance::where("id", $id)->select(\DB::raw("date(clock) as date"))->first()->date;


            $previous = Attendance::where("employee_id", $employeeId)->where("id", '<', $id)->where(\DB::raw("date(clock)"), $attDate)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();


            // if there is no other record before this record for the $attDate
            if (is_null($previous)) {


                $currentRow = Attendance::where("employee_id", $employeeId)->where("id", $id)->select(\DB::raw('date(clock) as date,time(clock) as time, clock,type,employee_id,id,note'))->first();
                $currentDate = new Carbon($currentRow->date);
                $currentTime = new Carbon($currentRow->time);


                $attendance = Attendance::where("id", $id)->select(\DB::raw('time(clock) attTime,clock,type,employee_id,id,note'))->first()->toArray();
                $systemGeneratedRecord = ['attTime' => $currentTime->subSeconds(120)->toTimeString(), 'clock' => $currentDate->toDateString() . ' ' . $currentTime->subSeconds(44)->toTimeString(), 'employee_id' => $currentRow->employee_id, 'id' => '', 'note' => 'This is temporary system generated note and time, please proceed to edit the details here for the Punch in', 'type' => 'in'];

                $result[] = $attendance;
                $result[] = $systemGeneratedRecord;

            } else {
                $result = Attendance::where(\DB::raw("date(clock)"), $attDate)->whereIn("id", [$id, --$id])->select(\DB::raw('time(clock) attTime,time(clock) as time,clock,type,employee_id,id,note'))->get();


            }


            // original record before the edit
//            $result = Attendance::whereIn("id", [$id,])->select(\DB::raw('time(clock) attTime,clock,time(clock) as time, type,employee_id,id,note'))->get();

        }


        return response()->json($result);

    }


}

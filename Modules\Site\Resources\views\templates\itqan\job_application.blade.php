@extends('home.layouts.home')

@section('page_title')
    {{trans('home_header.job_application')}}
@endsection
<style>
    body {
        font-family: 'Roboto', sans-serif;
        font-size: 16px;
        font-weight: 300;
        color: #888;
        line-height: 30px;
    }
    .f1-buttons {
        direction: ltr !important;
    }
</style>
@section('content')
    <div class="row helvetica">
        <div class="col-sm-10 col-sm-offset-1 col-md-8 col-md-offset-2 col-lg-6 col-lg-offset-3 form-box">

            {!! Form::open(['url' => '/home/<USER>/save_application','name'=>'f2','id'=>'registrationform' ,'class' => 'f1','files' => true]) !!}

            <h3>{{trans('home_header.job_application')}}</h3>

            <div class="f1-steps">

                <div class="f1-progress">
                    <div class="f1-progress-line" data-now-value="12.5" data-number-of-steps="6"
                         style="width: 12.5%;"></div>
                </div>

                <div class="f1-step active">
                    <div class="f1-step-icon"><i class="fa fa-question-circle"></i></div>
                    <p>{{trans('home_content.terms')}}</p>
                </div>


                <div class="f1-step">
                    <div class="f1-step-icon"><i class="fa fa-question-circle"></i></div>
                    <p>{{trans('home_content.personal_information')}}</p>
                </div>

                <div class="f1-step">
                    <div class="f1-step-icon"><i class="fa fa-user"></i></div>
                    <p>{{trans('home_content.contact_details')}}</p>
                </div>

                <div class="f1-step">
                    <div class="f1-step-icon"><i class="fa fa-phone"></i></div>
                    <p>{{trans('home_content.languages')}}</p>
                </div>

                <div class="f1-step">
                    <div class="f1-step-icon"><i class="fa fa-line-chart"></i></div>
                    <p>{{trans('home_content.qualifications_experiences_skills')}}</p>
                </div>

                <div class="f1-step">
                    <div class="f1-step-icon"><i class="fa fa-hourglass-end"></i></div>
                    <p>{{trans('home_content.other')}}</p>
                </div>

            </div>


            <fieldset>
                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.terms')}} :</h4>
                <div class="text-center panel-footer">
                    <?php echo htmlspecialchars_decode(cache('register_specifications'))?>
                </div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-next">{{trans('home_content.accept')}}</button>
                </div>
            </fieldset>


            <fieldset>

                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.personal_information')}} :</h4>


                <img src="{{URL::to('home_style/images/sys_user/default.png')}}" id="imgmodel"
                     style="max-width: 100px;border-radius: 50%;height: 100px;max-height: 100px;" class="center-block">

                <div class="form-group text-center {{ $errors->has('avatar') ? 'has-error' : ''}}">
                    {!! Form::label('avatar', trans('home_content.avatar'), ['class' => ' control-label']) !!}
                    <div class="">
                        <input type="file" name="avatar" id="avatar" class="inputfile inputfile-2 "
                               onchange="imgaeload(this)" accept=".png,.jpg,.jpeg,.gif"/>
                        <label for="avatar">
                            <span class="glyphicon glyphicon-compressed"> </span> {{trans('home_content.avatar')}} &hellip;
                        </label>
                        {!! $errors->first('avatar', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('desired_position') ? 'has-error' : ''}}">
                    {!! Form::label('desired_position', trans('home_content.desired_position'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('desired_position', null, ['class' => 'form-control']) !!}
                        <label class="label label-warning">
                            <i class="fa fa-info-circle"></i>
                            {{trans('home_content.teacher')}}
                            -
                            {{trans('home_content.administrative')}}
                            ...
                        </label>
                        {!! $errors->first('desired_position', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="form-group {{ $errors->has('full_name_ar') ? 'has-error' : ''}}">
                    {!! Form::label('full_name_ar', trans('home_content.full_name_ar'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('full_name_ar', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('full_name_ar', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group {{ $errors->has('full_name_en') ? 'has-error' : ''}}">
                    {!! Form::label('full_name_en', trans('home_content.full_name_en'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('full_name_en', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('full_name_en', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('gender') ? 'has-error' : ''}}">
                    {!! Form::label('gender', trans('home_content.gender'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::select('gender', [trans('home_content.male'), trans('home_content.female')], null, ['class' => 'form-control']) !!}
                        {!! $errors->first('gender', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group {{ $errors->has('birth_day') ? 'has-error' : ''}}">
                    {!! Form::label('birth_day', trans('home_content.birth_day'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::date('birth_day',date('Y-m-d'), ['class' => 'form-control']) !!}
                        {!! $errors->first('birth_day', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('marital_status') ? 'has-error' : ''}}">
                    {!! Form::label('marital_status', trans('home_content.marital_status'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::select('marital_status', [trans("home_content.single"), trans("home_content.married"), trans("home_content.divorced"), trans("home_content.widow_widower")], null, ['class' => 'form-control']) !!}
                        {!! $errors->first('marital_status', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('nationality') ? 'has-error' : ''}}">
                    {!! Form::label('nationality',trans('home_content.nationality'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        <?php $nations = getNationality(); ?>
                        <select class="selectpicker form-control" data-live-search="true" name="nationality">
                            @foreach($nations as $nation)
                                <option style="color: black !important;" value="{{$nation}}"
                                        data-tokens="{{$nation}}">{{$nation}}</option>
                            @endforeach
                        </select>

                        {!! $errors->first('nationality', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('ic_or_passport_no') ? 'has-error' : ''}}">
                    {!! Form::label('ic_or_passport_no', trans('home_content.ic_or_passport_no'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('ic_or_passport_no', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('ic_or_passport_no', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('visa_type') ? 'has-error' : ''}}">
                    {!! Form::label('visa_type', trans('home_content.visa_type'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::select('visa_type', [trans('home_content.student'),trans('home_content.worker'),trans('home_content.social_visit'),trans('home_content.dependent'),trans('home_content.other')], null, ['class' => 'form-control']) !!}
                        {!! $errors->first('visa_type', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('visa_expiry_date') ? 'has-error' : ''}}">
                    {!! Form::label('visa_expiry_date', trans('home_content.visa_expiry_date'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::date('visa_expiry_date', date('Y-m-d'), ['class' => 'form-control']) !!}
                        {!! $errors->first('visa_expiry_date', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('possibility_of_visa_renewal') ? 'has-error' : ''}}">
                    {!! Form::label('possibility_of_visa_renewal', trans('home_content.possibility_of_visa_renewal'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::select('possibility_of_visa_renewal', [trans('home_content.possible'), trans('home_content.not_possible')], null, ['class' => 'form-control']) !!}
                        {!! $errors->first('possibility_of_visa_renewal', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('education_level') ? 'has-error' : ''}}">
                    {!! Form::label('education_level',trans('home_content.education_level'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::select('education_level', [trans('home_content.less_than_high_school'),trans('home_content.hight_school'), trans('home_content.diploma'), trans('home_content.bachlor_degree'), trans('home_content.master_level'), trans('home_content.phd')], null, ['class' => 'form-control']) !!}
                        {!! $errors->first('education_level', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('specialization') ? 'has-error' : ''}}">
                    {!! Form::label('specialization', trans('home_content.specialization'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('specialization', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('specialization', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('quran_memorize') ? 'has-error' : ''}}">
                    {!! Form::label('quran_memorize', trans('home_content.quran_memorize'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('quran_memorize', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('quran_memorize', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('riwayah') ? 'has-error' : ''}}">
                    {!! Form::label('riwayah',  trans('home_content.riwayah'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('riwayah', null, ['class' => 'form-control']) !!}
                        <label class="label label-warning">
                            <i class="fa fa-info-circle"></i>
                            {{trans('home_content.hafs')}}
                            -
                            {{trans('home_content.notavailable')}}
                            ...
                        </label>
                        {!! $errors->first('riwayah', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="form-group {{ $errors->has('ijazah_in_quran') ? 'has-error' : ''}}">
                    {!! Form::label('ijazah_in_quran', trans('home_content.ijazah_in_quran'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::select('ijazah_in_quran', [ trans('home_content.yes'),  trans('home_content.no')], null, ['class' => 'form-control']) !!}
                        {!! $errors->first('ijazah_in_quran', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="clearfix"></div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-previous">{{trans('home_content.previous')}}</button>
                    <button type="button" class="btn btn-next">{{trans('home_content.next')}}</button>
                </div>
            </fieldset>


            <fieldset>
                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.contact_details')}} :</h4>
                <div class="form-group {{ $errors->has('mobile_phone') ? 'has-error' : ''}}">
                    {!! Form::label('mobile_phone',trans('home_content.tel'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('mobile_phone', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('mobile_phone', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group {{ $errors->has('email') ? 'has-error' : ''}}">
                    {!! Form::label('email', trans('home_content.email'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('email', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('email', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group {{ $errors->has('home_address') ? 'has-error' : ''}}">
                    {!! Form::label('home_address', trans('home_content.home_address'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::text('home_address', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('home_address', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>

                <div class="clearfix"></div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-previous">{{trans('home_content.previous')}}</button>
                    <button type="button" class="btn btn-next">{{trans('home_content.next')}}</button>
                </div>
            </fieldset>


            <fieldset>
                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.languages')}} :</h4>

                <div class="form-group {{ $errors->has('languages') ? 'has-error' : ''}}">

                    <table class="table">
                        <thead>
                        <tr>
                            <th>{{trans('home_content.lang')}}</th>
                            <th>{{trans('home_content.level')}}</th>
                        </tr>
                        </thead>

                        <tbody>

                        <tr>
                            <td>
                                {{trans('home_content.arabic')}}
                            </td>
                            <td>
                                <select class="col-md-12 form-control " name="level_ar">
                                    <option value="-1" selected
                                            disabled>{{trans('home_content.select_current_level')}}</option>
                                    <option value="1">{{trans('home_content.excellent')}}</option>
                                    <option value="2">{{trans('home_content.vgood')}}ً</option>
                                    <option value="3">{{trans('home_content.good')}}</option>
                                    <option value="4">{{trans('home_content.weak')}}</option>
                                    <option value="5">{{trans('home_content.n_a')}}</option>
                                </select>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                {{trans('home_content.english')}}
                            </td>
                            <td>
                                <select class="col-md-12 form-control " name="level_en">
                                    <option value="-1" selected
                                            disabled>{{trans('home_content.select_current_level')}}</option>
                                    <option value="1">{{trans('home_content.excellent')}}</option>
                                    <option value="2">{{trans('home_content.vgood')}}ً</option>
                                    <option value="3">{{trans('home_content.good')}}</option>
                                    <option value="4">{{trans('home_content.weak')}}</option>
                                    <option value="5">{{trans('home_content.n_a')}}</option>
                                </select>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                {{trans('home_content.malay')}}
                            </td>
                            <td>
                                <select class="col-md-12 form-control " name="level_ma">
                                    <option value="-1" selected
                                            disabled>{{trans('home_content.select_current_level')}}</option>
                                    <option value="1">{{trans('home_content.excellent')}}</option>
                                    <option value="2">{{trans('home_content.vgood')}}ً</option>
                                    <option value="3">{{trans('home_content.good')}}</option>
                                    <option value="4">{{trans('home_content.weak')}}</option>
                                    <option value="5">{{trans('home_content.n_a')}}</option>
                                </select>
                            </td>
                        </tr>


                        </tbody>
                    </table>

                </div>

                <div class="clearfix"></div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-previous">{{trans('home_content.previous')}}</button>
                    <button type="button" class="btn btn-next">{{trans('home_content.next')}}</button>
                </div>
            </fieldset>


            <fieldset>
                <h4 class="text-center"
                    style="padding: 25px;">{{trans('home_content.qualifications_experiences_skills')}} :</h4>
                <div class="form-group {{ $errors->has('positions_qualifications') ? 'has-error' : ''}}">
                    {!! Form::label('positions_qualifications', trans('home_content.positions_qualifications'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::textarea('positions_qualifications', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('positions_qualifications', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group {{ $errors->has('positions_experiences') ? 'has-error' : ''}}">
                    {!! Form::label('positions_experiences', trans('home_content.positions_experiences'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::textarea('positions_experiences', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('positions_experiences', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group {{ $errors->has('quranicexperiences') ? 'has-error' : ''}}">
                    {!! Form::label('quranicexperiences', trans('home_content.quranicexperiences'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::textarea('quranicexperiences', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('quranicexperiences', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group {{ $errors->has('other_experiences') ? 'has-error' : ''}}">
                    {!! Form::label('other_experiences',  trans('home_content.other_experiences'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::textarea('other_experiences', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('other_experiences', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group {{ $errors->has('skills') ? 'has-error' : ''}}">
                    {!! Form::label('skills', trans('home_content.skills'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::textarea('skills', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('skills', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>


                <div class="clearfix"></div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-previous">{{trans('home_content.previous')}}</button>
                    <button type="button" class="btn btn-next">{{trans('home_content.next')}}</button>
                </div>
            </fieldset>


            <fieldset>
                <h4 class="text-center" style="padding: 25px;">{{trans('home_content.other')}} :</h4>
                <div class="form-group {{ $errors->has('join_reasons') ? 'has-error' : ''}}">
                    {!! Form::label('join_reasons',  trans('home_content.join_reasons'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::textarea('join_reasons', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('join_reasons', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group {{ $errors->has('remarks') ? 'has-error' : ''}}">
                    {!! Form::label('remarks', trans('home_content.remarks'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        {!! Form::textarea('remarks', null, ['class' => 'form-control']) !!}
                        {!! $errors->first('remarks', '<p class="help-block">:message</p>') !!}
                    </div>
                </div>
                <div class="form-group {{ $errors->has('cv') ? 'has-error' : ''}}">
                    {!! Form::label('cv', trans('home_content.cv'), ['class' => 'col-md-12 control-label']) !!}
                    <div class="col-md-12">
                        <input type="file" name="cv" id="cv" class="inputfile inputfile-2"
                               accept=".pdf,.doc,.docx,.jpeg,.jpg"/>
                        <label for="cv">
                            <span class="glyphicon glyphicon-compressed"> </span> {{trans('home_content.cv')}} &hellip;
                        </label>
                        {!! $errors->first('cv', '<p class="help-block">:message</p>') !!}
                    </div>

                </div>


                <div class="clearfix"></div>
                <br/>
                <div class="f1-buttons">
                    <button type="button" class="btn btn-previous">{{trans('home_content.previous')}}</button>
                    <button type="submit" class="btn btn-primary">{{trans('home_content.finish')}}</button>
                </div>
            </fieldset>


            {!! Form::close() !!}
        </div>
    </div>
    <script>
        function imgaeload(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $('#imgmodel').attr('src', e.target.result);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }

        function add_lang_input(element) {
        }


    </script>



@endsection

@section('test')

@endsection



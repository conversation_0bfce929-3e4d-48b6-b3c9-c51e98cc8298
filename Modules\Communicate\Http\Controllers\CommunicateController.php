<?php

namespace Modules\Communicate\Http\Controllers;

use AfricasTalking\SDK\AfricasTalking;
use App\ApiBaseMethod;
use App\Classes;
use App\EmailSetting;
use App\EmailSmsLog;
use App\GeneralSettings;
use App\NoticeBoard;
use App\Notification;
use App\Guardian;
use App\Role;
use App\sGateway;
use App\Employee;
use App\Student;
use App\User;
use App\YearCheck;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Modules\Communicate\Http\Requests\RoleRequestCommunicationModule;
use Modules\Communicate\Http\Requests\SendSMSEmailRequestCommunicationModule;
use Modules\Communicate\Jobs\SendEmailJob;


//use App\Role;

class CommunicateController extends Controller
{


    public function noticeList(Request $request)
    {

        try {


            $allNotices = NoticeBoard::where('active_status', 1)
                ->orderBy('id', 'DESC')
                ->get();


            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse($allNotices, null);
            }


            return view('communicate::noticeList', compact('allNotices'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function sendMessage(Request $request)
    {


        try {
//            $roles = Role::where(function ($q) {
            $roles = Role::where(function ($q) {
//                $q->orWhere('type', 'System');
                $q;
            })->get();


            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse($roles, null);
            }

            return view('communicate::sendMessage', compact('roles'));
        } catch (\Exception $e) {
//            Toastr::error('Operation Failed', 'Failed');
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }

    public function saveNoticeData(Request $request)
    {

        // return $request;
        $input = $request->all();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'notice_title' => "required|max:50",
                'notice_date' => "required",
                'publish_on' => "required",
                'login_id' => "required",
            ]);
        } else {
            $validator = Validator::make($input, [
                'notice_title' => "required|max:50",
                'notice_date' => "required",
                'publish_on' => "required",
            ]);
        }


        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $roles_array = array();
            if (empty($request->role)) {
                $roles_array = '';
            } else {
                $roles_array = implode(',', $request->role);
            }

            $user = Auth()->user();

            if ($user) {
                $login_id = $user->id;
            } else {
                $login_id = $request->login_id;
            }


            $noticeData = new NoticeBoard();

            if (isset($request->is_published)) {
                $noticeData->is_published = $request->is_published;
            }
            $noticeData->notice_title = $request->notice_title;
            $noticeData->notice_message = $request->notice_message;

            $noticeData->notice_date = date('Y-m-d', strtotime($request->notice_date));
            $noticeData->publish_on = date('Y-m-d', strtotime($request->publish_on));

            // $noticeData->notice_date = Carbon::createFromFormat('m/d/Y', $request->notice_date)->format('Y-m-d');
            // $noticeData->publish_on = Carbon::createFromFormat('m/d/Y', $request->publish_on)->format('Y-m-d');

            $noticeData->inform_to = $roles_array;
            $noticeData->created_by = $login_id;
            $noticeData->organization_id = Auth::user()->organization_id;
            $noticeData->academic_id = YearCheck::getAcademicId();


            $results = $noticeData->save();


            if ($request->role != null) {

                foreach ($request->role as $key => $role) {


                    $userRoles = \DB::table("model_has_roles")->where("role_id", $role)->get();


                    foreach ($userRoles as $roleUserDetails) {

                        $userModel = $roleUserDetails->model_type;
                        $users = $userModel::where('status', 'active')->where('id', $roleUserDetails->model_id)->first();

                        // return $users;
                        foreach ($users as $key => $user) {
                            $notidication = new Notification();
                            $notidication->role_id = $role;
                            $notidication->message = "Notice for you";
                            $notidication->date = $noticeData->notice_date;
                            $notidication->user_id = $user->id;
                            $notidication->academic_id = YearCheck::getAcademicId();
                            $notidication->save();
                        }
                        // $notidication->user_id=$user->id;
                    }
                }
            }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($results) {
                    return ApiBaseMethod::sendResponse(null, 'Class Room has been created successfully');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($results) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect('notice-list');
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
//            Toastr::error('Operation Failed', 'Failed');
            Toastr::error($e->getMessage(), 'Failed');
            return redirect()->back();
        }
    }

    public function administratorNotice(Request $request)
    {
        try {

            $allNotices = AdministratorNotice::where('inform_to', Auth::user()->organization_id)

                ->get();
            // return $allNotices;
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse($allNotices, null);
            }
            return view('modules.site.templates.wajeha.backEnd.communicate.administratorNotice', compact('allNotices'));
        } catch (\Exception $e) {
            // dd($e);
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function editNotice(Request $request, $notice_id)
    {

        try {
            $roles = Role::where(function ($q) {
                $q->orWhere('type', 'System');
            })->get();
            $noticeDataDetails = NoticeBoard::find($notice_id);

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                $data = [];
                $data['roles'] = $roles->toArray();
                $data['noticeDataDetails'] = $noticeDataDetails->toArray();
                return ApiBaseMethod::sendResponse($data, null);
            }
            return view('modules.site.templates.wajeha.backEnd.communicate.editSendMessage', compact('noticeDataDetails', 'roles'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function updateNoticeData(Request $request)
    {
        $input = $request->all();
        if (ApiBaseMethod::checkUrl($request->fullUrl())) {
            $validator = Validator::make($input, [
                'notice_title' => "required|max:50",
                'notice_date' => "required",
                'publish_on' => "required",
                'login_id' => "required",
            ]);
        } else {
            $validator = Validator::make($input, [
                'notice_title' => "required|max:50",
                'notice_date' => "required",
                'publish_on' => "required",
            ]);
        }

        if ($validator->fails()) {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendError('Validation Error.', $validator->errors());
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $roles_array = array();
            if (empty($request->role)) {
                $roles_array = '';
            } else {
                $roles_array = implode(',', $request->role);
            }

            $user = Auth()->user();

            if ($user) {
                $login_id = $user->id;
            } else {
                $login_id = $request->login_id;
            }

            $noticeData = NoticeBoard::find($request->notice_id);
            if (isset($request->is_published)) {
                $noticeData->is_published = $request->is_published;
            }
            $noticeData->notice_title = $request->notice_title;
            $noticeData->notice_message = $request->notice_message;

            $noticeData->notice_date = date('Y-m-d', strtotime($request->notice_date));
            $noticeData->publish_on = date('Y-m-d', strtotime($request->publish_on));

            // return $request->notice_date;
            $noticeData->notice_date = Carbon::createFromFormat('m/d/Y', $request->notice_date)->format('Y-m-d');
            $noticeData->publish_on = Carbon::createFromFormat('m/d/Y', $request->publish_on)->format('Y-m-d');
            $noticeData->inform_to = $roles_array;
            $noticeData->updated_by = $login_id;
            $results = $noticeData->update();

            if ($request->role != null) {

                foreach ($request->role as $key => $role) {


                    $users = User::where('role_id', $role)->get();
                    // return $users;
                    foreach ($users as $key => $user) {
                        $notidication = new Notification();
                        $notidication->role_id = $role;
                        $notidication->message = $request->notice_title;
                        $notidication->date = $noticeData->notice_date;
                        $notidication->user_id = $user->id;
                        $notidication->academic_id = YearCheck::getAcademicId();
                        $notidication->save();
                    }
                    // $notidication->user_id=$user->id;


                }
            }

            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($results) {
                    return ApiBaseMethod::sendResponse(null, 'Notice has been updated successfully');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again');
                }
            } else {
                if ($results) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect('notice-list');
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function deleteNoticeView(Request $request, $id)
    {

        try {
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                return ApiBaseMethod::sendResponse($id, null);
            }
            return view('modules.site.templates.wajeha.backEnd.communicate.deleteNoticeView', compact('id'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function deleteNotice(Request $request, $id)
    {

        try {
            $result = NoticeBoard::destroy($id);
            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
                if ($result) {
                    return ApiBaseMethod::sendResponse(null, 'Notice has been deleted successfully');
                } else {
                    return ApiBaseMethod::sendError('Something went wrong, please try again.');
                }
            } else {
                if ($result) {
                    Toastr::success('Operation successful', 'Success');
                    return redirect()->back();
                } else {
                    Toastr::error('Operation Failed', 'Failed');
                    return redirect()->back();
                }
            }
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function sendEmailSmsView(Request $request)
    {
        try {
//            $roles = Role::select('*')->where('id', '!=', 1)->where(function ($q) {
            $roles = Role::select('*')->whereNotIn('id', [1, 2])->withCount('users')->cursor();
            $classes = Classes::cursor();


//            if (ApiBaseMethod::checkUrl($request->fullUrl())) {
//                $data = [];
//                $data['roles'] = $roles->toArray();
//                $data['classes'] = $classes->toArray();
//                return ApiBaseMethod::sendResponse($data, null);
//            }

            return view('modules.site.templates.wajeha.backEnd.communicate.sendEmailSms', compact('roles', 'classes'));
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }


    public function sendEmailFromComunicate($data, $to_name, $to_email, $email_sms_title)
    {


        $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);


        $systemEmail = EmailSetting::find(1);


        $system_email = $systemEmail->from_email;
        $organization_name = $systemSetting->organization_name;

        // return $system_email;
        if (!empty($system_email)) {

            $data['email_sms_title'] = $email_sms_title;
            $data['system_email'] = $system_email;
            $data['organization_name'] = $organization_name;

            $details = $to_email;

            dispatch(new SendEmailJob($data, $details));


            // $result = Mail::send('backEnd.emails.mail', ["result" => $data], function ($message) use ($to_name, $to_email, $email_sms_title, $system_email, $organization_name) {
            //     $message->to($to_email, $to_name)->subject($email_sms_title);
            //     $message->from($system_email, $organization_name);
            // });

            $error_data = [];
            return true;
        } else {
            $error_data[0] = 'success';
            $error_data[1] = 'Operation Failed, Please Updated System Mail';
            return $error_data;
        }
    }

    public function sendSMSFromComunicate($to_mobile, $sms)
    {

        $activeSmsGateway = sGateway::where('active_status', '=', 1)->first();

        if ($activeSmsGateway->gateway_name == 'Twilio') {
            // this is for school wise sms setting in saas.
            config(['TWILIO.SID' => $activeSmsGateway->twilio_account_sid]);
            config(['TWILIO.TOKEN' => $activeSmsGateway->twilio_authentication_token]);
            config(['TWILIO.FROM' => $activeSmsGateway->twilio_registered_no]);


            $account_id = $activeSmsGateway->twilio_account_sid; // Your Account SID from www.twilio.com/console
            $auth_token = $activeSmsGateway->twilio_authentication_token; // Your Auth Token from www.twilio.com/console
            $from_phone_number = $activeSmsGateway->twilio_registered_no;


            $client = new Twilio\Rest\Client($account_id, $auth_token);


            if (!empty($to_mobile)) {
                $result = $message = $client->messages->create($to_mobile, array('from' => $from_phone_number, 'body' => $sms));
            }
        } //end Twilio
        elseif ($activeSmsGateway->gateway_name == 'Clickatell') {


            // config(['clickatell.api_key' => $activeSmsGateway->clickatell_api_id]); //set a variale in config file(clickatell.php)

            $clickatell = new \Clickatell\Rest();

            $result = $clickatell->sendMessage(['to' => $to_mobile, 'content' => $sms]);
        } //end Clickatell
        elseif ($activeSmsGateway->gateway_name == 'Msg91') {
            $msg91_authentication_key_sid = $activeSmsGateway->msg91_authentication_key_sid;
            $msg91_sender_id = $activeSmsGateway->msg91_sender_id;
            $msg91_route = $activeSmsGateway->msg91_route;
            $msg91_country_code = $activeSmsGateway->msg91_country_code;

            $curl = curl_init();

            $url = "https://api.msg91.com/api/sendhttp.php?mobiles=" . $to_mobile . "&authkey=" . $msg91_authentication_key_sid . "&route=" . $msg91_route . "&sender=" . $msg91_sender_id . "&message=" . $sms . "&country=91";

            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true, CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 30, CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "GET", CURLOPT_SSL_VERIFYHOST => 0, CURLOPT_SSL_VERIFYPEER => 0,
            ));
            $response = curl_exec($curl);
            $err = curl_error($curl);
            curl_close($curl);
            if ($err) {
                $result = "cURL Error #:" . $err;
            } else {
                $result = $response;
            }
        } //end Msg91
        elseif ($activeSmsGateway->gateway_name == 'AfricaTalking') {


            $username = $activeSmsGateway->africatalking_username; // use 'sandbox' for development in the test environment
            $apiKey = $activeSmsGateway->africatalking_api_key; // use your sandbox app API key for development in the test environment
            $AT = new AfricasTalking($username, $apiKey);

            // Get one of the services
            $sms_Send = $AT->sms();


            // $to_mobile = implode(',', $to_mobile);

            // Use the service
            $result = $sms_Send->send([
                'to' => $to_mobile,
                'message' => $sms
            ]);


        }

        return $result;
    }

    public function sendEmailSms(SendSMSEmailRequestCommunicationModule $request)
    {


        try {

            $email_sms_title = $request->email_sms_title;
            // save data in email sms log
            $saveEmailSmsLogData = new EmailSmsLog();
            $saveEmailSmsLogData->saveEmailSmsLogData($request);
            if (empty($request->selectTab) or $request->selectTab == 'G') {

                if (empty($request->role)) {
                    Toastr::error('Please select whom you want to send', 'Failed');
                    return redirect()->back();
                } else {


                    if ($request->send_through == 'E') {

                        $email_sms_title = $request->email_sms_title;
                        $description = $request->description;
                        $message_to = implode(',', $request->role);

                        $to_name = [];
                        $to_email = [];
                        $to_mobile = [];
                        $receiverDetails = '';

                        foreach ($request->role as $role_id) {

                            if ($role_id == Role::where('name', 'student')->first()->id) {
                                $receiverDetails = Student::select('email', 'full_name', 'mobile')->get();
                            } elseif ($role_id == Role::where('name', 'parent')->first()->id) {
                                $receiverDetails = Guardian::select('guardians_email as email', 'fathers_name as full_name', 'fathers_mobile as mobile')->get();
                            } else {
                                $receiverDetails = Employee::select('email', 'full_name', 'mobile')->whereHas('roles', function ($q) use ($role_id) {

                                    return $q->where('id', $role_id);
                                })->get();
                            }




                            foreach ($receiverDetails as $receiverDetail) {
                                $to_name[] = $receiverDetail->full_name;
                                $to_email[] = $receiverDetail->email;
                                $to_mobile[] = $receiverDetail->mobile;

                                // send dynamic content in $data

                            }
                        }


                        $data = array('name' => $to_name, 'email_sms_title' => $request->email_sms_title, 'description' => $request->description);


                        $flag = $this->sendEmailFromComunicate($data, $to_name, $to_email, $email_sms_title);

                        // return gettype($flag);
                        if (!$flag) {
                            Toastr::error('Operation Failed lolz' . $flag[1], 'Failed');
                            return redirect()->back();
                        } else {
                            Toastr::success('Operation successful', 'Success');
                            return redirect()->back();
                        }
                    } else {

                        $email_sms_title = $request->email_sms_title;
                        $description = $request->description;
                        $message_to = implode(',', $request->role);

                        $to_name = '';
                        $to_email = '';
                        $to_mobile = '';
                        $receiverDetails = '';

                        foreach ($request->role as $role_id) {

                            if ($role_id == 23) {
                                $receiverDetails = Student::select('email', 'full_name', 'mobile')->all();
                            } elseif (\Auth::guard("web")->user()->hasRole("parent")) {
                                $receiverDetails = Guardian::select('guardians_email as email', 'fathers_name as full_name', 'fathers_mobile as mobile')->get();
                            } else {
                                $receiverDetails = Employee::select('email', 'full_name', 'mobile')->where('role_id', $role_id)->all();
                            }


                            foreach ($receiverDetails as $receiverDetail) {
                                $to_name = $receiverDetail->full_name;
                                $to_email = $receiverDetail->email;
                                $to_mobile = $receiverDetail->mobile;

                                // send dynamic content in $data
                                $data = array('name' => $to_name, 'email_sms_title' => $request->email_sms_title, 'description' => $request->description);

                                $sms = $request->description;

                                $this->sendSMSFromComunicate($to_mobile, $sms);
                            } //end loop
                        } //end role loop
                    }
                } //end else Please select whom you want to send

            } //end select tab G
            else if ($request->selectTab == 'I') {


                if (empty($request->message_to_individual)) {
                    Toastr::error('Please select whom you want to send', 'Failed');
                    return redirect()->back();
                } else {

                    if ($request->send_through == 'E') {

                        $message_to_individual = $request->message_to_individual;
                        $to_email = [];
                        $to_mobile = [];
                        foreach ($message_to_individual as $key => $value) {

                            $receiver_full_name_email = explode('-', $value);

                            $receiver_full_name = $receiver_full_name_email[0];
                            $receiver_email = $receiver_full_name_email[1];
                            $receiver_mobile = $receiver_full_name_email[2];

                            $to_name = $receiver_full_name;
                            $to_email[] = $receiver_email;

                            $to_mobile[] = $receiver_mobile;
                        }
                        // send dynamic content in $data

                        $data = array('name' => $to_name, 'email_sms_title' => $request->email_sms_title, 'description' => $request->description);


                        $flag = $this->sendEmailFromComunicate($data, $to_name, $to_email, $email_sms_title);

                        if (!$flag) {
                            Toastr::error('Operation Failed', 'Failed');
                            return redirect()->back();
                        }
                    } else {


                        $message_to_individual = $request->message_to_individual;


                        foreach ($message_to_individual as $key => $value) {
                            $receiver_full_name_email = explode('-', $value);
                            $receiver_full_name = $receiver_full_name_email[0];
                            $receiver_email = $receiver_full_name_email[1];
                            $receiver_mobile = $receiver_full_name_email[2];

                            $to_name = $receiver_full_name;
                            $to_email = $receiver_email;

                            $to_mobile = $receiver_mobile;
                            // send dynamic content in $data
                            $data = array('name' => $to_name, 'email_sms_title' => $request->email_sms_title, 'description' => $request->description);
                            // If checked Email


                            $sms = $request->description;
                            $this->sendSMSFromComunicate($to_mobile, $sms);
                        }
                    }
                } //end else
                Toastr::success('Operation successful', 'Success');
                return redirect()->back();
            } else {

                //  start send email/sms to class section
                if (empty($request->class_id)) {
                    Toastr::error('Please select whom you want to send', 'Failed');
                    return redirect()->back();
                } else {

                    if ($request->send_through == 'E') {


                        $class_id = $request->class_id;

                        $to_name = [];
                        $to_email = [];
                        $to_mobile = [];
                        $students = Student::whereHas('joint_classes', function ($q) use ($class_id) {


                            return $q->where('class_id', $class_id);

                        })->select('email', 'full_name', 'mobile')->get();


                        foreach ($students as $student) {
                            $to_name[] = $student->full_name;
                            $to_email[] = $student->email;
                            $to_mobile[] = $student->mobile;
                            // send dynamic content in $data

                        }
                            $data = array(
//                                'name' => $student->full_name,
                                'email_sms_title' => $request->email_sms_title,
                                'description' => $request->description,

                            );


                        $flag = $this->sendEmailFromComunicate($data, $to_name, $to_email, $email_sms_title);
                        if (!$flag) {
                            Toastr::error('Operation Failed' . $flag[1], 'Failed');
                            return redirect()->back();
                        }
                    } else {

                        $class_id = $request->class_id;
                        $selectedSections = $request->message_to_section;
                        foreach ($selectedSections as $key => $value) {
                            $students = Student::select('email', 'full_name', 'mobile')->where('class_id', $class_id)->where('section_id', $value)->all();

                            foreach ($students as $student) {
                                $to_name = $student->full_name;
                                $to_email = $student->email;
                                $to_mobile = $student->mobile;
                                // send dynamic content in $data
                                $data = array(
                                    'name' => $student->full_name,
                                    'email_sms_title' => $request->email_sms_title,
                                    'description' => $request->description,

                                );


                                $sms = $request->description;
                                $this->sendSMSFromComunicate($to_mobile, $sms);
                            } //end student loop
                        } //end selectedSections loop

                    }
                } //end else

                Toastr::success('Operation successful', 'Success');
                return redirect()->back();

            } //end else
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    } // end function sendEmailSms


    public function studStaffByRole(RoleRequestCommunicationModule $request)
    {

        try {

            if ($request->id == 23) {
                $allStudents = Student::all();
                return response()->json([$allStudents]);
//                $students = [];
//                foreach ($allStudents as $allStudent) {
//                    $students[] = Student::find($allStudent->id);
//                }


            }

            if ($request->id == 24) {
                $allParents = Guardian::all();
                return response()->json([$allParents]);

//                $parents = [];
//                foreach ($allParents as $allParent) {
//                    $parents[] = Guardian::find($allParent->id);
//                }

            }

            if ($request->id != 23 and $request->id != 24) {
                $allStaffs = Employee::whereHas('roles', function ($q) use ($request) {

                    return $q->where('id', $request->id);

                })->get();
                return response()->json([$allStaffs]);
//                $employees = [];
//                foreach ($allStaffs as $employeesvalue) {
//                    $employees[] = Employee::find($employeesvalue->id);
//                }

                return response()->json([$employees]);
            }
        } catch (\Exception $e) {
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }

    public function emailSmsLog()
    {
        try {
            $emailSmsLogs = EmailSmsLog::orderBy('id', 'DESC')->get();
            return view('modules.site.templates.wajeha.backEnd.communicate.emailSmsLog', compact('emailSmsLogs'));
        } catch (\Exception $e) {
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }
    }
}

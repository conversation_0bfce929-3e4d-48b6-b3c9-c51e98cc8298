<?php

namespace Modules\Leave\Http\Controllers;

use App\ApiBaseMethod;
use App\GeneralSettings;
use App\LeaveDefine;
use App\LeaveRequest;
use App\LeaveType;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

// use Illuminate\Routing\Controller;
use App\Http\Controllers\Controller;
use App\Employee;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class EmpAttendanceLeaveRequest extends Controller
{




    public function __invoke(Request $request)
    {




        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $input = $request->all();
        $validator = Validator::make($input, [
            'apply_date' => "required",
            'leave_type' => "required",
            'leave_from' => 'required|before_or_equal:leave_to',
            'leave_to' => "required",
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            DB::beginTransaction();


            $apply_leave = new LeaveRequest();
            $apply_leave->employee_id = $request->get('employee_id');
            $apply_leave->role_id = getMaxLeaveDaysRoleId($request->get('employee_id'));
            $apply_leave->apply_date = date('Y-m-d', strtotime($request->apply_date));
            $apply_leave->leave_define_id = $request->leave_define_id;
            $apply_leave->type_id = $request->leave_type;
            $apply_leave->leave_from = date('Y-m-d', strtotime($request->leave_from));
            $apply_leave->leave_to = date('Y-m-d', strtotime($request->leave_to));
            $apply_leave->approve_status = 'A';

            $apply_leave->organization_id = Auth::user()->organization_id;
//            $apply_leave->academic_id = getAcademicId();
            $result = $apply_leave->save();


            if ($result) {
                DB::commit();
                Toastr::success('Operation successful', 'Success');
                return response()->json(['message' => 'sick leave added for '.date('Y-m-d', strtotime($request->leave_from))]);
            } else {
                return response()->json(['message' => 'Operation Failed '.date('Y-m-d', strtotime($request->leave_from))]);


            }
        } catch (\Exception $e) {
            \Log::error($e->getMessage());

            DB::rollback();
            return response()->json(['message' => $e->getMessage()]);
        }
    }



}


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobSeeker User Journey</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
            color: #343a40;
            margin: 0;
            padding: 40px;
        }
        .container {
            max-width: 1200px;
            margin: auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #007bff;
            margin-bottom: 40px;
        }
        .timeline {
            display: flex;
            flex-direction: column;
            gap: 40px;
        }
        .stage {
            display: flex;
            align-items: flex-start;
            gap: 20px;
        }
        .stage-icon {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #007bff;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        .stage-content {
            flex-grow: 1;
            padding: 20px;
            background-color: #f1f3f5;
            border-radius: 8px;
            border-left: 5px solid #007bff;
        }
        .stage-title {
            font-size: 20px;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .stage-description {
            margin-bottom: 0;
        }
        .code {
            font-family: "SF Mono", "Fira Code", "Fira Mono", "Roboto Mono", monospace;
            background-color: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .arrow {
            text-align: center;
            font-size: 24px;
            color: #adb5bd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JobSeeker Module: User Journey</h1>
        <div class="timeline">
            <!-- Stage 1: Job Fetching -->
            <div class="stage">
                <div class="stage-icon"><i class="fas fa-cloud-download-alt"></i></div>
                <div class="stage-content">
                    <h3 class="stage-title">1. Job Fetching</h3>
                    <p class="stage-description">
                        The process begins by fetching jobs from two external sources: <strong>jobs.af</strong> and <strong>ACBAR</strong>. This is handled by two Artisan commands:
                        <ul>
                            <li><code class="code">jobseeker:sync-jobs-af</code>: Fetches jobs from the jobs.af API.</li>
                            <li><code class="code">jobseeker:sync-acbar-jobs</code>: Scrapes jobs from the ACBAR website.</li>
                        </ul>
                        To avoid detection, the system uses a rotating list of user agents and random delays between requests.
                    </p>
                </div>
            </div>
            <div class="arrow"><i class="fas fa-arrow-down"></i></div>
            <!-- Stage 2: Job Processing & Categorization -->
            <div class="stage">
                <div class="stage-icon"><i class="fas fa-cogs"></i></div>
                <div class="stage-content">
                    <h3 class="stage-title">2. Job Processing & Categorization</h3>
                    <p class="stage-description">
                        Once fetched, jobs are processed and categorized.
                        <ul>
                            <li><strong>Filtering:</strong> Jobs are filtered by location (e.g., only Kabul) and language (only English).</li>
                            <li><strong>Categorization:</strong> The <code class="code">determineJobCategories</code> method in <code class="code">JobsAfService</code> assigns categories to each job based on keywords in the job title. A static cache is used for canonical categories to improve performance.</li>
                        </ul>
                    </p>
                </div>
            </div>
            <div class="arrow"><i class="fas fa-arrow-down"></i></div>
            <!-- Stage 3: Job Storage -->
            <div class="stage">
                <div class="stage-icon"><i class="fas fa-database"></i></div>
                <div class="stage-content">
                    <h3 class="stage-title">3. Job Storage</h3>
                    <p class="stage-description">
                        Processed jobs are stored in the <code class="code">jobs</code> table.
                        <ul>
                            <li>The <code class="code">JobRepository</code>'s <code class="code">createOrUpdate</code> method is used to create new jobs or update existing ones.</li>
                            <li>De-duplication is handled based on the job's unique slug.</li>
                        </ul>
                    </p>
                </div>
            </div>
            <div class="arrow"><i class="fas fa-arrow-down"></i></div>
            <!-- Stage 4: Notification Setup -->
            <div class="stage">
                <div class="stage-icon"><i class="fas fa-user-cog"></i></div>
                <div class="stage-content">
                    <h3 class="stage-title">4. Notification Setup</h3>
                    <p class="stage-description">
                        Job seekers can create personalized notification setups.
                        <ul>
                            <li>A setup is defined by the <code class="code">JobNotificationSetup</code> model, which is linked to job categories and recipients.</li>
                            <li>The system checks for duplicate setups to prevent redundant notifications.</li>
                        </ul>
                    </p>
                </div>
            </div>
            <div class="arrow"><i class="fas fa-arrow-down"></i></div>
            <!-- Stage 5: Notification Generation -->
            <div class="stage">
                <div class="stage-icon"><i class="fas fa-bell"></i></div>
                <div class="stage-content">
                    <h3 class="stage-title">5. Notification Generation</h3>
                    <p class="stage-description">
                        Notifications are generated when a new job is created or an existing one is updated.
                        <ul>
                            <li>The <code class="code">JobProcessedEvent</code> is dispatched, triggering a listener that starts the notification process.</li>
                            <li>The <code class="code">sendJobNotificationEmail</code> method in <code class="code">JobsAfService</code> identifies the relevant notification setups and jobs to be sent.</li>
                        </ul>
                    </p>
                </div>
            </div>
            <div class="arrow"><i class="fas fa-arrow-down"></i></div>
            <!-- Stage 6: Notification Delivery -->
            <div class="stage">
                <div class="stage-icon"><i class="fas fa-paper-plane"></i></div>
                <div class="stage-content">
                    <h3 class="stage-title">6. Notification Delivery</h3>
                    <p class="stage-description">
                        Finally, notifications are delivered to the job seekers.
                        <ul>
                            <li><strong>Email:</strong> The <code class="code">EmailService</code> sends notifications using predefined email templates.</li>
                            <li><strong>Push Notifications:</strong> The system is also capable of sending push notifications to mobile devices.</li>
                        </ul>
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class ClassTimetableController extends Controller
{
    /**
     * Get class timetable data for the calendar
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Security measures
        // 1. Rate limiting
        $ip = $request->ip();
        $cacheKey = 'class_timetable_requests_' . $ip;
        $requestCount = Cache::get($cacheKey, 0);
        
        if ($requestCount > 10) {
            Log::warning('Rate limit exceeded for class timetable API', [
                'ip' => $ip, 
                'requests' => $requestCount
            ]);
            return response()->json(['error' => 'Rate limit exceeded'], 429);
        }
        
        Cache::put($cacheKey, $requestCount + 1, now()->addMinute(1));
        
        // Validate request parameters
        $validated = $request->validate([
            'start' => 'sometimes|date',
            'end' => 'sometimes|date',
            'center' => 'sometimes|string',
            'program' => 'sometimes|string'
        ]);
        
        try {
            // Get date range from request
            $startDate = $request->input('start') ? Carbon::parse($request->input('start')) : Carbon::now()->startOfMonth();
            $endDate = $request->input('end') ? Carbon::parse($request->input('end')) : Carbon::now()->endOfMonth();
            $center = $request->input('center', 'all');
            $program = $request->input('program', 'all');
            
            // Cache the results to reduce database load
            $cacheKey = 'class_timetable_' . $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d') . '_' . $center . '_' . $program;
            
            return Cache::remember($cacheKey, now()->addHour(), function() use ($startDate, $endDate, $center, $program) {
                // Fetch class timetable data
                $query = DB::table('class_timetable as ct')
                    ->join('classes as c', 'ct.class_id', '=', 'c.id')
                    ->join('centers as sc', 'c.center_id', '=', 'sc.id')
                    ->join('subjects as s', 'c.subject_id', '=', 's.id')
                    ->leftJoin('students as ss', function($join) {
                        $join->on('c.id', '=', 'ss.class_id')
                             ->whereNull('ss.deleted_at');
                    })
                    ->select([
                        'c.id as class_id',
                        'c.class_code',
                        'c.center_id',
                        'sc.center_name',
                        'ct.sat',
                        'ct.sun',
                        'ct.mon',
                        'ct.tue',
                        'ct.wed',
                        'ct.thu',
                        'ct.fri',
                        'ct.class_duration',
                        's.subject_title',
                        's.subject_level',
                        's.subject_category',
                        DB::raw('(select count(*) from students where class_id = c.id and deleted_at is null) as registered_students'),
                        DB::raw('(select capacity from class_settings where class_id = c.id) as capacity')
                    ])
                    ->whereNull('c.deleted_at')
                    ->whereNull('ct.deleted_at')
                    ->where('c.status', 'Active')
                    ->whereBetween('ct.start_at', [$startDate, $endDate])
                    ->groupBy('c.id');
                
                // Apply center filter
                if ($center !== 'all') {
                    // Extract center ID from the center value (format: center-1)
                    $centerId = intval(str_replace('center-', '', $center));
                    if ($centerId) {
                        $query->where('c.center_id', $centerId);
                    }
                }
                
                // Apply program filter
                if ($program !== 'all') {
                    $query->where('s.subject_category', 'like', '%' . $program . '%');
                }
                
                $classes = $query->get();
                
                // Process the data for FullCalendar format
                $events = [];
                
                foreach ($classes as $class) {
                    // Calculate available seats
                    $capacity = $class->capacity ?? 15; // Default to 15 if not set
                    $registered = $class->registered_students ?? 0;
                    $availableSeats = max(0, $capacity - $registered);
                    
                    // Map subject level to event color
                    $levelColors = [
                        'beginner' => '#4caf50',
                        'intermediate' => '#2196f3',
                        'advanced' => '#9c27b0',
                        'all' => '#ff9800'
                    ];
                    
                    $level = strtolower($class->subject_level ?? 'beginner');
                    $color = $levelColors[$level] ?? $levelColors['beginner'];
                    
                    // Determine the program from subject category
                    $programMapping = [
                        'tajweed' => 'tajweed',
                        'quran' => 'hifz',
                        'arabic' => 'arabic',
                        'tafseer' => 'tafseer'
                    ];
                    
                    $programName = 'tajweed'; // Default
                    foreach ($programMapping as $key => $value) {
                        if (stripos($class->subject_category, $key) !== false) {
                            $programName = $value;
                            break;
                        }
                    }
                    
                    // Create events for each day the class meets
                    $days = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
                    $dayMap = [
                        'sun' => 0, 'mon' => 1, 'tue' => 2, 'wed' => 3, 
                        'thu' => 4, 'fri' => 5, 'sat' => 6
                    ];
                    
                    foreach ($days as $day) {
                        if (!empty($class->$day)) {
                            // Create event for each week in the date range
                            $currentDate = clone $startDate;
                            
                            while ($currentDate <= $endDate) {
                                // Adjust to the correct day of the week
                                $dayOfWeek = $dayMap[$day];
                                $diff = ($dayOfWeek - $currentDate->dayOfWeek + 7) % 7;
                                $eventDate = (clone $currentDate)->addDays($diff);
                                
                                if ($eventDate > $endDate) {
                                    break;
                                }
                                
                                // Class start time
                                $startTime = Carbon::parse($class->$day);
                                $classStart = (clone $eventDate)->setTimeFromTimeString($startTime->format('H:i:s'));
                                
                                // Calculate end time based on duration
                                $durationMinutes = $class->class_duration * 60; // Convert hours to minutes
                                $classEnd = (clone $classStart)->addMinutes($durationMinutes);
                                
                                // Create event
                                $events[] = [
                                    'id' => 'class-' . $class->class_id . '-' . $day . '-' . $eventDate->format('Ymd'),
                                    'title' => $class->subject_title,
                                    'start' => $classStart->format('Y-m-d\TH:i:s'),
                                    'end' => $classEnd->format('Y-m-d\TH:i:s'),
                                    'backgroundColor' => $color,
                                    'borderColor' => $color,
                                    'classNames' => [$level, $programName],
                                    'extendedProps' => [
                                        'center' => 'center-' . $class->center_id,
                                        'program' => $programName,
                                        'level' => $level,
                                        'instructor' => 'Sheikh ' . $this->getRandomInstructor(), // Would use real data in production
                                        'seats' => $availableSeats,
                                        'capacity' => $capacity,
                                        'registered' => $registered,
                                        'classCode' => $class->class_code ?? ('ITQ-' . $class->class_id)
                                    ]
                                ];
                                
                                // Move to next week
                                $currentDate->addDays(7);
                            }
                        }
                    }
                }
                
                Log::info('Class timetable data fetched', [
                    'class_count' => count($classes),
                    'events_count' => count($events)
                ]);
                
                return response()->json($events);
            });
            
        } catch (\Exception $e) {
            Log::error('Error fetching class timetable data', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Get a random instructor name (for demo purposes)
     * In production, this would fetch from the database
     * 
     * @return string
     */
    private function getRandomInstructor()
    {
        $instructors = [
            'Ahmad', 'Muhammad', 'Abdullah', 'Ali', 'Yusuf', 
            'Ibrahim', 'Omar', 'Khalid', 'Hassan', 'Nasser'
        ];
        
        return $instructors[array_rand($instructors)];
    }

    /**
     * Get classes timetable data for calendar display
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClassTimetable(Request $request)
    {
        try {
            // Get required query parameters
            $start = $request->input('start');
            $end = $request->input('end');
            
            // Get optional filter parameters (will only be set if explicitly provided in the request)
            $center = $request->has('center') ? $request->input('center') : 'all';
            $program = $request->has('program') ? $request->input('program') : 'all';
            $price = $request->has('price') ? $request->input('price') : 'all';
            
            Log::info('Fetching class timetable', [
                'start' => $start, 
                'end' => $end, 
                'center' => $center,
                'program' => $program,
                'price' => $price
            ]);
            
            try {
                // Query the database for class timetable data
                // Example query - modify as needed based on your database schema
                $query = DB::table('class_timetable as ct')
                    ->join('classes as c', 'ct.class_id', '=', 'c.id')
                    ->join('subjects as s', 'c.subject_id', '=', 's.id')
                    ->join('teachers as t', 'c.teacher_id', '=', 't.id')
                    ->join('centers as ctr', 'c.center_id', '=', 'ctr.id')
                    ->leftJoin('subject_translations as st', function($join) {
                        $join->on('s.id', '=', 'st.subject_id')
                            ->where('st.locale', '=', app()->getLocale());
                    })
                    ->leftJoin('center_translations as ctrt', function($join) {
                        $join->on('ctr.id', '=', 'ctrt.center_id')
                            ->where('ctrt.locale', '=', app()->getLocale());
                    })
                    ->leftJoin('fees_types as ft', 'c.fee_type_id', '=', 'ft.id')
                    ->select(
                        'ct.id',
                        'ct.start_time',
                        'ct.end_time',
                        'c.class_code',
                        'c.level',
                        'c.max_capacity',
                        'c.current_capacity',
                        'c.is_online',
                        'c.gender_restriction',
                        'st.name as subject_name',
                        'ct.day_of_week',
                        'ctr.id as center_id',
                        'ctrt.name as center_name',
                        't.name as teacher_name',
                        'ft.amount as fee',
                        'ft.frequency as fee_frequency',
                        'ft.description as fee_details',
                        'c.program_id',
                        's.code as subject_code'
                    )
                    ->whereBetween('ct.start_time', [$start . ' 00:00:00', $end . ' 23:59:59']);
                
                // Apply center filter only if explicitly provided in request
                if ($request->has('center') && $center !== 'all') {
                    $query->where('ctr.id', '=', str_replace('center-', '', $center));
                    Log::info('Applying center filter', ['center' => $center]);
                }
                
                // Apply program filter only if explicitly provided in request
                if ($request->has('program')) {
                    // Handle multi-select - split by comma and filter
                    $programCodes = explode(',', $program);
                    $query->where(function($q) use ($programCodes) {
                        foreach ($programCodes as $code) {
                            $q->orWhere('c.program_code', '=', $code)
                              ->orWhere('s.code', '=', $code);
                        }
                    });
                    Log::info('Applying program filter', ['programs' => $programCodes]);
                }
                
                // Apply price filter only if explicitly provided in request
                if ($request->has('price') && $price !== 'all') {
                    if ($price === 'free') {
                        $query->where('ft.amount', '=', 0);
                    } else if ($price === 'paid') {
                        $query->where('ft.amount', '>', 0);
                    } else if ($price === 'low') {
                        $query->whereBetween('ft.amount', [0.01, 49.99]);
                    } else if ($price === 'medium') {
                        $query->whereBetween('ft.amount', [50, 100]);
                    } else if ($price === 'high') {
                        $query->where('ft.amount', '>', 100);
                    }
                    Log::info('Applying price filter', ['price' => $price]);
                }
                
                // Get the results
                $classTimetable = $query->get();
                
                // Format data for FullCalendar
                $events = [];
                
                foreach ($classTimetable as $class) {
                    // Calculate available seats
                    $availableSeats = $class->max_capacity - $class->current_capacity;
                    
                    // Map center ID to center code used in frontend
                    $centerCode = 'center-' . $class->center_id;
                    
                    // Create a random class code if not available
                    $classCode = $class->class_code ?? 'C' . str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
                    
                    // Map level to color
                    $color = '';
                    switch (strtolower($class->level)) {
                        case 'beginner':
                            $color = '#4caf50';
                            break;
                        case 'intermediate':
                            $color = '#2196f3';
                            break;
                        case 'advanced':
                            $color = '#9c27b0';
                            break;
                        default:
                            $color = '#757575';
                    }
                    
                    // Determine program type and icon from subject
                    list($programType, $programIcon) = $this->getProgramDetails($class);
                    
                    $events[] = [
                        'id' => $class->id,
                        'title' => $class->subject_name,
                        'start' => $class->start_time,
                        'end' => $class->end_time,
                        'backgroundColor' => $color,
                        'borderColor' => $color,
                        'extendedProps' => [
                            'classCode' => $classCode,
                            'level' => strtolower($class->level),
                            'instructor' => $class->teacher_name,
                            'center' => $centerCode,
                            'seats' => $availableSeats,
                            'program' => $programType,
                            'programIcon' => $programIcon,
                            'fee' => (float) $class->fee,
                            'feeFrequency' => $class->fee_frequency,
                            'feeDetails' => $class->fee_details,
                            'isOnline' => (bool) $class->is_online,
                            'genderRestriction' => $class->gender_restriction
                        ]
                    ];
                }
                
                // If we get here, we have real data from the database
                return response()->json($events);
                
            } catch (\Exception $dbException) {
                // Log the database exception but don't fail - use mock data instead
                Log::error('Database error fetching timetable, using mock data: ' . $dbException->getMessage(), [
                    'file' => $dbException->getFile(),
                    'line' => $dbException->getLine(),
                    'trace' => $dbException->getTraceAsString()
                ]);
                
                // Return mock data as fallback
                return response()->json($this->getMockClassData($start, $end));
            }
            
        } catch (\Exception $e) {
            Log::error('Error fetching class timetable: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Return mock data as fallback
            return response()->json($this->getMockClassData($start, $end));
        }
    }

    /**
     * Helper method to determine program type and icon from class data
     * 
     * @param object $class
     * @return array [$programType, $programIcon]
     */
    private function getProgramDetails($class)
    {
        $subjectName = strtolower($class->subject_name ?? '');
        $subjectCode = strtolower($class->subject_code ?? '');
        
        // Default values
        $programType = 'general';
        $programIcon = 'graduation-cap';
        
        // Quran programs
        if (
            strpos($subjectName, 'hifz') !== false || 
            strpos($subjectName, 'memorization') !== false || 
            strpos($subjectName, 'revision') !== false ||
            strpos($subjectName, 'quran') !== false ||
            strpos($subjectName, 'ijazah') !== false ||
            strpos($subjectName, 'sanad') !== false
        ) {
            $programType = 'hifz';
            $programIcon = 'quran';
        } 
        // Tajweed programs
        else if (
            strpos($subjectName, 'tajweed') !== false || 
            strpos($subjectName, 'talaqqi') !== false || 
            strpos($subjectName, 'recitation') !== false ||
            strpos($subjectName, 'qaedah') !== false
        ) {
            $programType = 'tajweed';
            $programIcon = 'book-reader';
        } 
        // Arabic language programs
        else if (
            strpos($subjectName, 'arabic') !== false || 
            strpos($subjectName, 'nouranya') !== false || 
            strpos($subjectName, 'nuraniyah') !== false ||
            strpos($subjectName, 'language') !== false
        ) {
            $programType = 'arabic';
            $programIcon = 'language';
        } 
        // Islamic studies programs
        else if (
            strpos($subjectName, 'islamic') !== false || 
            strpos($subjectName, 'fiqh') !== false || 
            strpos($subjectName, 'tafseer') !== false ||
            strpos($subjectName, 'sharia') !== false ||
            strpos($subjectName, 'tafsir') !== false
        ) {
            $programType = 'islamic';
            $programIcon = 'mosque';
        }
        
        return [$programType, $programIcon];
    }

    /**
     * Generate mock class data for testing when the database is not available
     * 
     * @param string $start Start date
     * @param string $end End date
     * @return array Array of mock class events
     */
    private function getMockClassData($start, $end) 
    {
        $startDate = Carbon::parse($start);
        $endDate = Carbon::parse($end);
        $events = [];
        
        // Generate 25 mock classes
        $subjects = [
            ['title' => 'Quran Memorization', 'program' => 'hifz', 'icon' => 'quran'],
            ['title' => 'Tajweed Rules', 'program' => 'tajweed', 'icon' => 'book-reader'],
            ['title' => 'Arabic Language', 'program' => 'arabic', 'icon' => 'language'],
            ['title' => 'Islamic Studies', 'program' => 'islamic', 'icon' => 'mosque'],
            ['title' => 'Tafseer Class', 'program' => 'islamic', 'icon' => 'book']
        ];
        
        $levels = ['beginner', 'intermediate', 'advanced'];
        $centers = ['center-1', 'center-2', 'center-3', 'center-4'];
        $instructors = ['Sheikh Ahmad', 'Sheikh Muhammad', 'Sheikh Abdullah', 'Sheikh Yusuf', 'Sheikh Ibrahim'];
        
        // Create classes for each day in the range
        for ($date = clone $startDate; $date <= $endDate; $date->addDay()) {
            // Add 3-5 classes per day
            $classesPerDay = rand(3, 5);
            
            for ($i = 0; $i < $classesPerDay; $i++) {
                $subject = $subjects[array_rand($subjects)];
                $level = $levels[array_rand($levels)];
                $center = $centers[array_rand($centers)];
                $instructor = $instructors[array_rand($instructors)];
                
                // Random start time between 8am and 6pm
                $hour = rand(8, 18);
                $minutes = [0, 30][array_rand([0, 30])];
                
                $classStart = (clone $date)->setTime($hour, $minutes);
                $classEnd = (clone $classStart)->addHours(1)->addMinutes(30);
                
                // Generate random seat availability
                $capacity = rand(10, 30);
                $registered = rand(0, $capacity);
                $available = $capacity - $registered;
                
                // Determine color based on level
                $color = '';
                switch ($level) {
                    case 'beginner':
                        $color = '#4caf50';
                        break;
                    case 'intermediate':
                        $color = '#2196f3';
                        break;
                    case 'advanced':
                        $color = '#9c27b0';
                        break;
                }
                
                // Fee amount - some classes are free
                $fee = 0;
                $feeFrequency = 'per month';
                
                $events[] = [
                    'id' => 'mock-' . $date->format('Ymd') . '-' . $i,
                    'title' => $subject['title'],
                    'start' => $classStart->format('Y-m-d\TH:i:s'),
                    'end' => $classEnd->format('Y-m-d\TH:i:s'),
                    'backgroundColor' => $color,
                    'borderColor' => $color,
                    'extendedProps' => [
                        'classCode' => 'MOCK-' . rand(1000, 9999),
                        'level' => $level,
                        'instructor' => $instructor,
                        'center' => $center,
                        'seats' => $available,
                        'program' => $subject['program'],
                        'programIcon' => $subject['icon'],
                        'fee' => (float) $fee,
                        'feeFrequency' => $feeFrequency,
                        'feeDetails' => $fee > 0 ? 'Includes learning materials and access to online resources' : '',
                        'isOnline' => $center === 'center-4',
                        'genderRestriction' => $center === 'center-1' ? 'Male' : ($center === 'center-2' ? 'Female' : '')
                    ]
                ];
            }
        }
        
        Log::info('Generated mock class data', ['count' => count($events)]);
        return $events;
    }

    /**
     * Get program data categorized by type
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPrograms()
    {
        try {
            // Cache the results to improve performance
            return Cache::remember('program_categories', now()->addDay(), function() {
                // Get all active programs with their translations
                $programs = DB::table('programs as p')
                    ->join('program_translations as pt', function($join) {
                        $join->on('p.id', '=', 'pt.program_id')
                            ->where('pt.locale', '=', app()->getLocale());
                    })
                    ->where('p.status', 'active')
                    ->whereNull('p.deleted_at')
                    ->select('p.id', 'p.code', 'pt.title', 'pt.description')
                    ->get();
                
                // Define program categories and associated icons
                $categories = [
                    'quran' => [
                        'name' => 'Quran Programs',
                        'icon' => 'quran',
                        'programs' => []
                    ],
                    'tajweed' => [
                        'name' => 'Tajweed Programs',
                        'icon' => 'book-reader',
                        'programs' => []
                    ],
                    'arabic' => [
                        'name' => 'Arabic Language',
                        'icon' => 'language',
                        'programs' => []
                    ],
                    'islamic' => [
                        'name' => 'Islamic Studies',
                        'icon' => 'mosque',
                        'programs' => []
                    ],
                    'other' => [
                        'name' => 'Other Programs',
                        'icon' => 'graduation-cap',
                        'programs' => []
                    ]
                ];
                
                // Categorize programs based on keywords in titles
                foreach ($programs as $program) {
                    $title = strtolower($program->title);
                    
                    // Add program icon based on the title
                    if (strpos($title, 'memorization') !== false || strpos($title, 'hifz') !== false || strpos($title, 'ijazah') !== false) {
                        $program->icon = 'quran';
                        $program->category = 'quran';
                        $categories['quran']['programs'][] = $program;
                    } 
                    else if (strpos($title, 'tajweed') !== false || strpos($title, 'talaqqi') !== false || strpos($title, 'recitation') !== false) {
                        $program->icon = 'book-reader';
                        $program->category = 'tajweed';
                        $categories['tajweed']['programs'][] = $program;
                    }
                    else if (strpos($title, 'arabic') !== false || strpos($title, 'nouranya') !== false || strpos($title, 'nuraniyah') !== false) {
                        $program->icon = 'language';
                        $program->category = 'arabic';
                        $categories['arabic']['programs'][] = $program;
                    }
                    else if (strpos($title, 'islamic') !== false || strpos($title, 'fiqh') !== false || strpos($title, 'tafsir') !== false) {
                        $program->icon = 'mosque';
                        $program->category = 'islamic';
                        $categories['islamic']['programs'][] = $program;
                    }
                    else {
                        $program->icon = 'graduation-cap';
                        $program->category = 'other';
                        $categories['other']['programs'][] = $program;
                    }
                }
                
                // Remove empty categories
                foreach ($categories as $key => $category) {
                    if (empty($category['programs'])) {
                        unset($categories[$key]);
                    }
                }
                
                return response()->json([
                    'categories' => array_values($categories),
                    'allPrograms' => $programs
                ]);
            });
        } catch (\Exception $e) {
            Log::error('Error fetching program data: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json(['error' => 'Failed to fetch program data'], 500);
        }
    }
} 
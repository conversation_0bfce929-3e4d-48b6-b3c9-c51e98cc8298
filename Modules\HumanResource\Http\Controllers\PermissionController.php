<?php

namespace Modules\HumanResource\Http\Controllers;

use App\Employee;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Event;
use Carbon\Carbon;
use App\EventMember;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Modules\RolePermission\Entities\Permission;

class PermissionController extends Controller
{

//    public function getPermissions(Request $request)
//    {
//        $employeeId = $request->get('employee_id');
//
//        // Fetch all permissions
//        $permissions = Permission::all()->map(function ($permission) use ($employeeId) {
//            $isAssigned = DB::table('model_has_permissions')
//                ->where('model_id', $employeeId)
//                ->where('permission_id', $permission->id)
//                ->exists();
//
//            return [
//                'id' => $permission->id,
//                'name' => $permission->name,
//                'description' => $permission->description,
//                'assigned' => $isAssigned
//            ];
//        });
//
//        return \DataTables::of($permissions)
//            ->make(true);
//    }

    public function getPermissions(Request $request)
    {
        $employeeId = $request->get('employee_id');

        // Fetch permissions with assigned status and sort in the query
        $permissions = Permission::select('permissions.*')
            ->leftJoin('model_has_permissions', function ($join) use ($employeeId) {
                $join->on('permissions.id', '=', 'model_has_permissions.permission_id')
                    ->where('model_has_permissions.model_id', '=', $employeeId);
            })
            ->addSelect(DB::raw('IF(model_has_permissions.permission_id IS NULL, 0, 1) as assigned'))
            ->where('permissions.guard_name', 'employee')
            ->where('permissions.organization_id', 2)
            ->where('permissions.status', 1)
            ->orderBy('assigned', 'desc')
            ->orderBy('permissions.name', 'asc')
            ->get();

        // Map the permissions to include 'assigned' status as boolean
        $permissions = $permissions->map(function ($permission) {
            return [
                'id'          => $permission->id,
                'name'        => $permission->name,
                'description' => $permission->description,
                'assigned'    => (bool) $permission->assigned,
            ];
        });

        return \DataTables::of($permissions)
            ->make(true);
    }





    public function assignPermission(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:users,id',
            'permission_id' => 'required|exists:permissions,id',
        ]);

        $employee = Employee::findOrFail($request->employee_id);
        $permission = Permission::where('id', $request->permission_id)
            ->where('guard_name', 'employee')
            ->where('organization_id', 2)
            ->where('status', 1)
            ->firstOrFail();

        if ($employee->hasDirectPermission($permission->name)) {
            return response()->json(['message' => 'Permission already assigned.'], 400);
        }

        $employee->givePermissionTo($permission->name);

        return response()->json(['message' => 'Permission assigned successfully.']);
    }


    public function assignPermissionsBulk(Request $request)
    {


        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ]);


        $employee = Employee::findOrFail($request->employee_id);
        $permissions = Permission::whereIn('id', $request->permissions)
            ->where('guard_name', 'employee')
            ->where('organization_id', 2)
            ->where('status', 1)
            ->get();

        // Check if any permissions were not found
        $foundPermissionIds = $permissions->pluck('id')->toArray();
        $requestedPermissionIds = $request->permissions;
        $missingPermissionIds = array_diff($requestedPermissionIds, $foundPermissionIds);
        
        if (!empty($missingPermissionIds)) {
            Log::warning('Permissions not found for employee guard', [
                'user_id' => auth()->id(),
                'employee_id' => $request->employee_id,
                'missing_permission_ids' => $missingPermissionIds,
                'requested_permission_ids' => $requestedPermissionIds,
                'found_permission_ids' => $foundPermissionIds,
            ]);
            
            return response()->json([
                'message' => 'Some permissions are not available for assignment.',
                'error' => 'Permission IDs ' . implode(', ', $missingPermissionIds) . ' are not valid for employee guard with organization_id=2 and status=1'
            ], 422);
        }

        logger()->info('Employee:', $employee->toArray());
        logger()->info('Permissions fetched:', $permissions->toArray());

        // Clear cache to ensure fresh permission data
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        foreach ($permissions as $permission) {
            try {


                if (!$employee->hasDirectPermission($permission->name, 'employee')) {
                    $employee->givePermissionTo($permission->name);
                }

                Log::info('Permission Assigned', [
                    'user_id' => auth()->id(),
                    'employee_id' => $employee->id,
                    'permission_id' => $permission->id,
                    'timestamp' => now(),
                ]);


            } catch (\Exception $e) {
                Log::error('Permission Assignment Error', [
                    'user_id' => auth()->id(),
                    'employee_id' => $employee->id,
                    'permission_id' => $permission->id,
                    'error' => $e->getMessage(),
                ]);
                return response()->json([
                    'message' => 'Failed to assign permissions.',
                    'error' => $e->getMessage()
                ], 500);

            }
        }

        return response()->json(['message' => 'Permissions assigned successfully!']);
    }

    public function revokePermission(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'permission_id' => 'required|exists:permissions,id',
        ]);

        $employee = Employee::findOrFail($request->employee_id);
        $permission = Permission::where('id', $request->permission_id)
            ->where('guard_name', 'employee')
            ->where('organization_id', 2)
            ->where('status', 1)
            ->firstOrFail();

        try {
            if ($employee->hasPermissionTo($permission->name, 'employee')) {
                $employee->revokePermissionTo($permission->name);
            }

            Log::info('Permission Revoked', [
                'user_id' => auth()->id(),
                'employee_id' => $employee->id,
                'permission_id' => $permission->id,
                'timestamp' => now(),
            ]);


        } catch (\Exception $e) {
            Log::error('Permission Revocation Error', [
                'user_id' => auth()->id(),
                'employee_id' => $employee->id,
                'permission_id' => $permission->id,
                'error' => $e->getMessage(),
            ]);
            return response()->json(['message' => 'Failed to revoke permission.'], 500);
        }

        return response()->json(['message' => 'Permission revoked successfully!']);
    }

    public function revokePermissionsBulk(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $employee = Employee::findOrFail($request->employee_id);
        $permissions = Permission::whereIn('id', $request->permissions)
            ->where('guard_name', 'employee')
            ->where('organization_id', 2)
            ->where('status', 1)
            ->get();

        foreach ($permissions as $permission) {
            try {
                if ($employee->hasPermissionTo($permission->name, 'employee')) {
                    $employee->revokePermissionTo($permission->name);
                }

                Log::info('Permission Revoked', [
                    'user_id' => auth()->id(),
                    'employee_id' => $employee->id,
                    'permission_id' => $permission->id,
                    'timestamp' => now(),
                ]);


            } catch (\Exception $e) {
                Log::error('Bulk Permission Revocation Error', [
                    'user_id' => auth()->id(),
                    'employee_id' => $employee->id,
                    'permission_id' => $permission->id,
                    'error' => $e->getMessage(),
                ]);
                return response()->json(['message' => 'Failed to revoke permissions.'], 500);

            }
        }

        return response()->json(['message' => 'Permissions revoked successfully!']);
    }

    public function createPermission(Request $request)
    {
        $request->validate([
            'name' => [
                'required',
                'string',
                'min:3',
                'max:255',
                'regex:/^[a-z\s\-_]+$/',
                'unique:permissions,name'
            ],
        ], [
            'name.required' => 'Permission name is required.',
            'name.min' => 'Permission name must be at least 3 characters long.',
            'name.max' => 'Permission name cannot exceed 255 characters.',
            'name.regex' => 'Permission name should contain only lowercase letters, spaces, hyphens, and underscores.',
            'name.unique' => 'A permission with this name already exists.',
        ]);

        try {
            // Create the permission using Spatie's Permission model
            $permission = Permission::create([
                'name' => $request->name,
                'guard_name' => 'employee',
                'organization_id' => 2,
                'status' => 1,
            ]);

            Log::info('Permission Created Successfully', [
                'user_id' => auth()->id(),
                'permission_id' => $permission->id,
                'permission_name' => $permission->name,
                'organization_id' => 2,
                'timestamp' => now(),
            ]);

            return response()->json([
                'message' => 'Permission "' . $permission->name . '" created successfully!',
                'permission' => [
                    'id' => $permission->id,
                    'name' => $permission->name,
                    'guard_name' => $permission->guard_name,
                    'organization_id' => $permission->organization_id,
                    'status' => $permission->status,
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Permission Creation Error', [
                'user_id' => auth()->id(),
                'permission_name' => $request->name,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => now(),
            ]);

            // Check if it's a duplicate key error
            if (str_contains($e->getMessage(), 'Duplicate entry')) {
                return response()->json([
                    'message' => 'A permission with this name already exists.',
                    'errors' => ['name' => ['A permission with this name already exists.']]
                ], 422);
            }

            return response()->json([
                'message' => 'Failed to create permission. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }


}

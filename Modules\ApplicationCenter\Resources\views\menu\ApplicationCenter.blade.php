@if(@in_array(542, App\GlobalVariable::GlobarModuleLinks()) || Auth::guard("web")->user()->hasRole("member"))
    <li>
        <a href="#subMenuStudentRegistration" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
            <span class="flaticon-reading"></span>
            Application
        </a>
        <ul class="collapse list-unstyled" id="subMenuStudentRegistration">
            {{--            @if(@in_array(543, App\GlobalVariable::GlobarModuleLinks()) || Auth::user()->role_id == 1 || \Auth::guard("web")->user()->hasRole("parent") /** 24 = parent role id */)--}}
            @if(@in_array(543, App\GlobalVariable::GlobarModuleLinks()) || Auth::user()->role_id == 1 || Auth::guard("web")->user()->hasRole("member"))
                <li>
                    <a href="{{route('application.list')}}"> My Applications</a>
                <li>
                    @if (Auth::guard('web')->user()->hasRole('parent'))
                        <a href="{{route('student.guardian.application.form')}}"> Apply</a>
                    @endif

                    @if (! (Auth::guard('web')->user()->hasRole('parent')))
                        <a href="{{route('student.application.form')}}"> Apply</a>

                    @endif
                </li>
                </li>
            @endif
            @if(App\GeneralSettings::isModule('Saas') != TRUE)
                @if(@in_array(547, App\GlobalVariable::GlobarModuleLinks()) || Auth::user()->role_id == 1)
                    <li>
                        <a href="{{url('applicationcenter/settings')}}"> @lang('lang.settings')</a>
                    </li>
                @endif
            @endif
        </ul>
    </li>
@endif

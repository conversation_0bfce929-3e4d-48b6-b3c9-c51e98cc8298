<?php

declare(strict_types=1);

namespace Modules\Education\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use App\Classes;
use App\Student;
use App\IjazasanadMemorizationPlan;
use App\StudentIjazasanadMemorizationReport;
use App\MoshafSurah;
use App\AttendanceOption;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

final class MonthEndIjazasanadSummaryController extends Controller
{
    public function __invoke(Request $request): JsonResponse
    {
        try {
            $classId = (int)$request->input('classId');
            $monthYear = $request->input('classDate');

            if (!$classId || !$monthYear) {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            // Parse month and year using the same logic as MonthlyIjazasanadReportController
            $date = Carbon::createFromFormat('M Y', $monthYear);
            $month = $date->month;
            $year = $date->year;

            // Get students with the same ordering as MonthlyPlanController
            $students = Student::whereHas('joint_classes', function ($query) use ($classId) {
                $query->where('class_id', $classId);
            })
            ->where('status', 'active')
            ->orderBy('full_name', 'asc') // Same ordering as MonthlyPlanController
            ->get();

            // Separate students by level for appropriate calculations
            $level1Students = [];
            $level2Students = [];
            
            foreach ($students as $student) {
                $student->loadMissing('studentProgramLevels.programlevel');
                $studentLevel = $this->detectStudentLevel($student);
                
                if ($studentLevel === 'level1') {
                    $level1Students[] = $student;
                } else {
                    $level2Students[] = $student; // Level 2 or fallback
                }
            }

            // Calculate summary metrics
            $studentCount = $students->count();
            $avgAttendance = $this->calculateAverageAttendance($students, $classId, $month, $year);
            $avgAchievement = $this->calculateAverageAchievement($students, $classId, $month, $year);
            
            // Calculate all metrics: level-specific + Juz + Pages
            $level1Metrics = $this->calculateLevel1Metrics(collect($level1Students), $month, $year, $classId);
            $level2Metrics = $this->calculateLevel2Metrics(collect($level2Students), $month, $year, $classId);
            $juzMetrics = $this->calculateJuzMetrics($students, $classId, $month, $year);
            $pageMetrics = $this->calculatePageMetrics($students, $classId, $month, $year);

            $data = [[
                'noOfStudents' => $studentCount,
                'avgAttendance' => $this->formatProgressBarWithPopup($avgAttendance, '#28a745', $this->getAttendanceBreakdown($students, $classId, $month, $year)),
                'avgAchievement' => $this->formatProgressBarWithPopup($avgAchievement, '#1fff0f', $this->getAchievementBreakdown($students, $classId, $month, $year)),
                'totalPlannedJuz' => $juzMetrics['planned'],
                'totalCompletedJuz' => $juzMetrics['completed'],
                'juzProgress' => $this->formatProgressBarWithPopup($juzMetrics['percentage'], '#007bff', $juzMetrics['details']),
                'totalPlannedPages' => $pageMetrics['planned'],
                'totalCompletedPages' => $pageMetrics['completed'],
                'pageProgress' => $this->formatProgressBarWithPopup($pageMetrics['percentage'], '#6f42c1', $pageMetrics['details'])
            ]];

            return DataTables::of($data)->rawColumns(['avgAttendance', 'avgAchievement', 'juzProgress', 'pageProgress'])->toJson();

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    private function calculateAverageAttendance($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) {
            return 0.0;
        }

        // Get total scheduled classes for the month
        $class = Classes::find($classId);
        if (!$class || !$class->timetable) {
            return 0.0;
        }

        $totalClasses = $class->timetable->daysCountPerMonth($month, $year);
        if ($totalClasses <= 0) {
            return 0.0;
        }

        $totalAttendancePercentage = 0;
        $validStudents = 0;

        foreach ($students as $student) {
            // Count attended classes (both on-time and late)
            $attendedClasses = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereIn('attendance_id', [1, 2]) // 1 = late, 2 = on time
                ->count();

            $attendancePercentage = min(100.0, ($attendedClasses / $totalClasses) * 100);
            $totalAttendancePercentage += $attendancePercentage;
            $validStudents++;
        }

        return $validStudents > 0 ? $totalAttendancePercentage / $validStudents : 0.0;
    }

    private function calculateAverageAchievement($students, int $classId, int $month, int $year): float
    {
        if ($students->isEmpty()) {
            return 0.0;
        }

        $totalAchievementPercentage = 0;
        $validStudents = 0;

        foreach ($students as $student) {
            // Load student program levels for level detection
            $student->loadMissing('studentProgramLevels.programlevel');
            
            // Get the student's plan for the month
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year, $classId) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                            ->whereMonth('start_date', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    });
                })
                ->first();

            if (!$plan) {
                continue;
            }

            // Get actual achievements from reports
            $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            if ($reports->isEmpty()) {
                continue;
            }

            // Detect student level and calculate achievement accordingly
            $studentLevel = $this->detectStudentLevel($student);
            
            if ($studentLevel === 'level1') {
                $completionData = $this->calculateLevel1Completion($plan, $reports);
                $achievementPercentage = $completionData['completion_rate'];
            } else {
                // Level 2 or fallback calculation
                $completionData = $this->calculateLevel2Completion($plan, $reports);
                $achievementPercentage = $completionData['completion_rate'];
            }
            
            if ($achievementPercentage > 0) {
                $totalAchievementPercentage += $achievementPercentage;
                $validStudents++;
            }
        }

        return $validStudents > 0 ? $totalAchievementPercentage / $validStudents : 0.0;
    }



    private function detectStudentLevel($studentDetails): ?string
    {
        // Eager load if not already loaded, though the main query should handle this.
        // Only call loadMissing if this is an Eloquent model
        if (method_exists($studentDetails, 'loadMissing')) {
            $studentDetails->loadMissing('studentProgramLevels.programlevel');
        }

        foreach ($studentDetails->studentProgramLevels as $studentProgramLevel) {
            if ($studentProgramLevel->programlevel) {
                $levelName = strtolower($studentProgramLevel->programlevel->title); // Use 'title' attribute from translations
                if (str_contains($levelName, 'level 1')) {
                    return 'level1';
                }
                if (str_contains($levelName, 'level 2')) {
                    return 'level2';
                }
            }
        }
        return null;
    }

    private function calculateLevel1Completion($plan, $reports): array
    {
        $components = [
            'talqeen',
            'revision',
            'jazariyah',
            'seminars'
        ];

        $componentDetails = [];
        $totalCompletion = 0;
        $validComponents = 0;

        foreach ($components as $componentName) {
            $fromField = "{$componentName}_from_lesson";
            $toField = "{$componentName}_to_lesson";

            if (!empty($plan->$fromField) && !empty($plan->$toField) && $plan->$fromField <= $plan->$toField) {
                $validComponents++;
                $plannedFrom = $plan->$fromField;
                $plannedTo = $plan->$toField;
                $plannedRangeCount = $plannedTo - $plannedFrom + 1;

                $completedLessons = $this->getUniqueCompletedLessonsForComponent($reports, $componentName);
                
                $plannedLessonsRange = range($plannedFrom, $plannedTo);
                $achievedInRange = count(array_intersect($completedLessons, $plannedLessonsRange));
                
                $componentProgress = ($plannedRangeCount > 0) ? ($achievedInRange / $plannedRangeCount) * 100 : 0;
                
                $totalCompletion += $componentProgress;
                $componentDetails[$componentName] = round($componentProgress, 2);
            } else {
                $componentDetails[$componentName] = 0;
            }
        }

        $overallCompletionRate = $validComponents > 0 ? ($totalCompletion / $validComponents) : 0;

        $tooltip = $validComponents === 0 ? "No valid lesson plans found" : null;
        if ($reports->count() === 0) {
            $tooltip = "No progress reports available";
        }

        return [
            'completion_rate' => round($overallCompletionRate, 2),
            'type' => 'level1',
            'components' => $componentDetails,
            'valid_components' => $validComponents,
            'tooltip' => $tooltip,
        ];
    }

    private function getUniqueCompletedLessonsForComponent($reports, string $componentName): array
    {
        $allLessons = [];
        $fromField = "{$componentName}_from_lesson";
        $toField = "{$componentName}_to_lesson";

        foreach ($reports as $report) {
            if (!empty($report->$fromField) && !empty($report->$toField) && $report->$fromField <= $report->$toField) {
                $allLessons = array_merge($allLessons, range($report->$fromField, $report->$toField));
            }
        }

        return array_unique($allLessons);
    }

    private function calculateLevel2Completion($plan, $reports): array
    {
        $plannedPages = 0;
        $tooltip = null;
        
        // Check if we have valid hefz plan coordinates
        if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && 
            !empty($plan->to_surat) && !empty($plan->to_ayat)) {
            
            try {
                // Use existing page calculation logic from the controller
                if ($plan->study_direction == 'backward') {
                    $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                        $plan->start_from_surat,
                        $plan->start_from_ayat,
                        $plan->to_surat,
                        $plan->to_ayat
                    ]);
                    $plannedPages = $numberofPages[0]->numberofPagesSum ?? 0;
                } else {
                    // Forward direction
                    DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                        $plan->start_from_surat,
                        $plan->start_from_ayat,
                        $plan->to_surat,
                        $plan->to_ayat
                    ]);
                    $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                    $plannedPages = $results[0]->number_of_pages_sum ?? 0;
                }
            } catch (\Exception $e) {
                \Log::error('Error calculating planned pages for Level 2 student: ' . $e->getMessage());
                $plannedPages = 0;
                $tooltip = "Error calculating planned pages";
            }
        } else {
            $tooltip = "Incomplete hefz plan coordinates";
        }
        
        // Calculate achieved pages from reports
        $achievedPages = $reports->sum('pages_memorized') ?? 0;
        
        // Cap achieved pages at planned pages maximum to prevent percentage > 100%
        if ($plannedPages > 0 && $achievedPages > $plannedPages) {
            $achievedPages = $plannedPages;
        }
        
        // Calculate completion percentage
        $percentage = $plannedPages > 0 ? round(($achievedPages / $plannedPages) * 100) : 0;
        
        // Set tooltip for edge cases
        if ($reports->count() === 0) {
            $tooltip = "No progress reports available";
        } elseif ($plannedPages === 0 && !$tooltip) {
            $tooltip = "No valid hefz plan found";
        }
        
        return [
            'completion_rate' => $percentage,
            'type' => 'level2',
            'planned_pages' => $plannedPages,
            'achieved_pages' => $achievedPages,
            'tooltip' => $tooltip,
        ];
    }

    private function calculateJuzMetrics($students, int $classId, int $month, int $year): array
    {
        if ($students->isEmpty()) {
            return [
                'planned' => 0,
                'completed' => 0,
                'percentage' => 0,
                'details' => ['planned_juz' => 0, 'completed_juz' => 0, 'percentage' => 0]
            ];
        }

        $totalPlannedJuz = 0;
        $totalCompletedJuz = 0;

        foreach ($students as $student) {
            // Get planned Juz from plan
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year, $classId) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                            ->whereMonth('start_date', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    });
                })
                ->first();

            if ($plan) {
                // Count planned Juz' based on from_surat_juz_id to to_surat_juz_id
                if ($plan->from_surat_juz_id && $plan->to_surat_juz_id) {
                    $plannedJuz = max(0, $plan->to_surat_juz_id - $plan->from_surat_juz_id + 1);
                    $totalPlannedJuz += $plannedJuz;
                }
            }

            // Get completed Juz from reports
            $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereNotNull('hefz_from_surat')
                ->whereNotNull('hefz_to_surat')
                ->get();

            if ($reports->isNotEmpty()) {
                $achievedJuz = $this->calculateAchievedJuzFromReports($reports);
                $totalCompletedJuz += $achievedJuz;
            }
        }

        $percentage = $totalPlannedJuz > 0 ? min(100, round(($totalCompletedJuz / $totalPlannedJuz) * 100, 1)) : 0;

        return [
            'planned' => $totalPlannedJuz,
            'completed' => $totalCompletedJuz,
            'percentage' => $percentage,
            'details' => [
                'planned_juz' => $totalPlannedJuz,
                'completed_juz' => $totalCompletedJuz,
                'percentage' => $percentage,
                'total_students' => $students->count(),
                'calculation_note' => 'Based on Surah-to-Juz mapping from memorization plans and reports'
            ]
        ];
    }

    private function calculateAchievedJuzFromReports($reports): int
    {
        $uniqueJuzSet = collect();

        foreach ($reports as $report) {
            // Get Juz information for the covered range
            $fromJuz = $this->getJuzFromSurahAyat($report->hefz_from_surat, $report->hefz_from_ayat);
            $toJuz = $this->getJuzFromSurahAyat($report->hefz_to_surat, $report->hefz_to_ayat);
            
            if ($fromJuz && $toJuz) {
                for ($juz = $fromJuz; $juz <= $toJuz; $juz++) {
                    $uniqueJuzSet->push($juz);
                }
            }
        }

        return $uniqueJuzSet->unique()->count();
    }

    private function getJuzFromSurahAyat(?int $surahId, ?int $ayat): ?int
    {
        if (!$surahId || !$ayat) {
            return null;
        }
        return (int)ceil($surahId / 4);
    }

    private function calculatePageMetrics($students, int $classId, int $month, int $year): array
    {
        if ($students->isEmpty()) {
            return [
                'planned' => 0,
                'completed' => 0,
                'percentage' => 0,
                'details' => ['planned_pages' => 0, 'completed_pages' => 0, 'percentage' => 0]
            ];
        }

        $totalPlannedPages = 0;
        $totalCompletedPages = 0;
        $studentsWithPlans = 0;

        foreach ($students as $student) {
            // Get planned pages from plan
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year, $classId) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                            ->whereMonth('start_date', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    });
                })
                ->first();

            if ($plan) {
                // Calculate planned pages using same logic as Level 2 calculation
                $plannedPages = 0;
                
                // Check if we have valid hefz plan coordinates
                if (!empty($plan->start_from_surat) && !empty($plan->start_from_ayat) && 
                    !empty($plan->to_surat) && !empty($plan->to_ayat)) {
                    
                    try {
                        // Use existing page calculation logic
                        if ($plan->study_direction == 'backward') {
                            $numberofPages = DB::select('CALL CountMemorizedNumberofPagesBackward(?, ?, ?, ?)', [
                                $plan->start_from_surat,
                                $plan->start_from_ayat,
                                $plan->to_surat,
                                $plan->to_ayat
                            ]);
                            $plannedPages = $numberofPages[0]->numberofPagesSum ?? 0;
                        } else {
                            // Forward direction
                            DB::select("CALL CountMemorizedNumberofPagesForward(?, ?, ?, ?, @number_of_pages_sum)", [
                                $plan->start_from_surat,
                                $plan->start_from_ayat,
                                $plan->to_surat,
                                $plan->to_ayat
                            ]);
                            $results = DB::select("SELECT @number_of_pages_sum as number_of_pages_sum");
                            $plannedPages = $results[0]->number_of_pages_sum ?? 0;
                        }
                        
                        $totalPlannedPages += $plannedPages;
                        $studentsWithPlans++;
                    } catch (\Exception $e) {
                        \Log::error('Error calculating planned pages for student ' . $student->id . ': ' . $e->getMessage());
                    }
                }
            }

            // Get completed pages from reports
            $completedPages = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->sum('pages_memorized') ?? 0;

            $totalCompletedPages += $completedPages;
        }

        $percentage = $totalPlannedPages > 0 ? min(100, round(($totalCompletedPages / $totalPlannedPages) * 100, 1)) : 0;

        return [
            'planned' => $totalPlannedPages,
            'completed' => $totalCompletedPages,
            'percentage' => $percentage,
            'details' => [
                'planned_pages' => $totalPlannedPages,
                'completed_pages' => $totalCompletedPages,
                'percentage' => $percentage,
                'total_students' => $students->count(),
                'students_with_plans' => $studentsWithPlans,
                'calculation_note' => 'Based on stored procedure calculations from memorization plans and reported pages memorized'
            ]
        ];
    }

    private function calculateLevel1Metrics($level1Students, int $month, int $year, int $classId): array
    {
        if ($level1Students->isEmpty()) {
            return [
                'planned_lessons' => 0,
                'completed_lessons' => 0,
                'students_count' => 0
            ];
        }

        $totalPlannedLessons = 0;
        $totalCompletedLessons = 0;

        foreach ($level1Students as $student) {
            // Get student's plan
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year, $classId) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                            ->whereMonth('start_date', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    });
                })
                ->first();

            if (!$plan) {
                continue;
            }

            // Get reports
            $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            if ($reports->isEmpty()) {
                continue;
            }

            // Calculate for all components
            $components = ['talqeen', 'revision', 'jazariyah', 'seminars'];
            
            foreach ($components as $componentName) {
                $fromField = "{$componentName}_from_lesson";
                $toField = "{$componentName}_to_lesson";

                if (!empty($plan->$fromField) && !empty($plan->$toField) && $plan->$fromField <= $plan->$toField) {
                    $plannedLessons = $plan->$toField - $plan->$fromField + 1;
                    $totalPlannedLessons += $plannedLessons;

                    $completedLessons = $this->getUniqueCompletedLessonsForComponent($reports, $componentName);
                    $plannedLessonsRange = range($plan->$fromField, $plan->$toField);
                    $achievedInRange = count(array_intersect($completedLessons, $plannedLessonsRange));
                    
                    $totalCompletedLessons += $achievedInRange;
                }
            }
        }

        return [
            'planned_lessons' => $totalPlannedLessons,
            'completed_lessons' => $totalCompletedLessons,
            'students_count' => $level1Students->count()
        ];
    }

    private function calculateLevel2Metrics($level2Students, int $month, int $year, int $classId): array
    {
        if ($level2Students->isEmpty()) {
            return [
                'planned_pages' => 0,
                'completed_pages' => 0,
                'students_count' => 0
            ];
        }

        $totalPlannedPages = 0;
        $totalCompletedPages = 0;

        foreach ($level2Students as $student) {
            // Get student's plan
            $plan = IjazasanadMemorizationPlan::where('student_id', $student->id)
                ->where(function ($query) use ($month, $year, $classId) {
                    $query->where(function ($q) use ($year, $month, $classId) {
                        $q->whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    })->orWhere(function ($q2) use ($year, $month, $classId) {
                        $q2->whereYear('start_date', $year)
                            ->whereMonth('start_date', $month)
                            ->where('class_id', $classId)
                            ->where('status', 'active');
                    });
                })
                ->first();

            if (!$plan) {
                continue;
            }

            // Get reports
            $reports = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            if ($reports->isEmpty()) {
                continue;
            }

            // Calculate Level 2 completion
            $completionData = $this->calculateLevel2Completion($plan, $reports);
            $totalPlannedPages += $completionData['planned_pages'];
            $totalCompletedPages += $completionData['achieved_pages'];
        }

        return [
            'planned_pages' => $totalPlannedPages,
            'completed_pages' => $totalCompletedPages,
            'students_count' => $level2Students->count()
        ];
    }



    private function formatProgressBarWithPopup(float $percentage, string $color, array $details): string
    {
        $roundedPercentage = round($percentage, 1);
        $detailsJson = htmlspecialchars(json_encode($details), ENT_QUOTES, 'UTF-8');
        
        return sprintf(
            '<div class="attendance-progress" onclick="showAttendanceDetails(this)" data-details=\'%s\' style="cursor: pointer; position: relative; background: #f0f0f0; border-radius: 4px; height: 20px; overflow: hidden;">
                <div class="bar" style="height: 100%%; background-color: %s; width: %s%%; border-radius: 4px; transition: width 0.3s ease; position: absolute; top: 0; left: 0;"></div>
                <div class="label" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 11px; z-index: 2; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">%s%%</div>
            </div>',
            $detailsJson,
            $color,
            $roundedPercentage,
            $roundedPercentage
        );
    }

    private function getAttendanceBreakdown($students, int $classId, int $month, int $year): array
    {
        // Get total scheduled classes for the month
        $class = Classes::find($classId);
        $totalClasses = $class && $class->timetable ? $class->timetable->daysCountPerMonth($month, $year) : 0;
        
        $totalStudents = $students->count();
        $totalPossibleAttendances = $totalStudents * $totalClasses;
        
        $totalAttended = 0;
        $totalAbsent = 0;

        foreach ($students as $student) {
            $attendedClasses = StudentIjazasanadMemorizationReport::where('student_id', $student->id)
                ->where('class_id', $classId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereIn('attendance_id', [1, 2]) // 1 = late, 2 = on time
                ->count();

            $totalAttended += $attendedClasses;
            $totalAbsent += max(0, $totalClasses - $attendedClasses);
        }

        $percentage = $totalPossibleAttendances > 0 ? round(($totalAttended / $totalPossibleAttendances) * 100, 1) : 0;

        return [
            'total_classes' => $totalClasses,
            'total_students' => $totalStudents,
            'total_possible' => $totalPossibleAttendances,
            'attended' => $totalAttended,
            'absent' => $totalAbsent,
            'percentage' => $percentage
        ];
    }

    private function getAchievementBreakdown($students, int $classId, int $month, int $year): array
    {
        $level1Students = [];
        $level2Students = [];
        
        foreach ($students as $student) {
            $student->loadMissing('studentProgramLevels.programlevel');
            $studentLevel = $this->detectStudentLevel($student);
            
            if ($studentLevel === 'level1') {
                $level1Students[] = $student;
            } else {
                $level2Students[] = $student;
            }
        }

        $level1Metrics = $this->calculateLevel1Metrics(collect($level1Students), $month, $year, $classId);
        $level2Metrics = $this->calculateLevel2Metrics(collect($level2Students), $month, $year, $classId);

        // Calculate overall percentage
        $totalPlanned = 0;
        $totalCompleted = 0;
        
        if (count($level1Students) > 0) {
            $totalPlanned += $level1Metrics['planned_lessons'];
            $totalCompleted += $level1Metrics['completed_lessons'];
        }
        
        if (count($level2Students) > 0) {
            $totalPlanned += $level2Metrics['planned_pages'];
            $totalCompleted += $level2Metrics['completed_pages'];
        }

        $percentage = $totalPlanned > 0 ? round(($totalCompleted / $totalPlanned) * 100, 1) : 0;

        return [
            'level1_students' => count($level1Students),
            'level2_students' => count($level2Students),
            'level1_planned' => $level1Metrics['planned_lessons'],
            'level1_completed' => $level1Metrics['completed_lessons'],
            'level2_planned' => $level2Metrics['planned_pages'],
            'level2_completed' => $level2Metrics['completed_pages'],
            'overall_percentage' => $percentage,
            'calculation_note' => 'Mixed calculation: Level 1 uses lessons, Level 2 uses pages'
        ];
    }
} 
<?php

namespace Modules\EducationalReports\Http\Controllers;

use App\Center;
use App\Classes;
use App\Employee;
use App\Student;
use App\StudentRevisionReport;
use App\Weekend;
use App\ClassRoom;
use App\YearCheck;
use App\ApiBaseMethod;
use Illuminate\Http\Request;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use PDF; // assuming you have the alias set for DOMPDF

class ItqanTablesPDFController extends Controller
{

    public function __construct()
	{
//        $this->middleware('PM');
        // User::checkAuth();
	}

    public function downloadPDF($monthYear)
    {



        // Convert the monthYear string to a Carbon instance
        $date = \Carbon\Carbon::createFromFormat('F Y', $monthYear);

// Extract the month and year separately
        $foundingDirector = "Dr Nashwan"; // Numeric representation of the month (e.g., 06)
        $monthName = $date->monthName; // Numeric representation of the month (e.g., 06)
        $month = $date->format('m'); // Numeric representation of the month (e.g., 06)
        $year = $date->format('Y'); // 4-digit year (e.g., 2023)
        $letterHead = asset("uploads/settings/EducationDivisionLetterheadersEDU.jpg");
        $educationManager = 'To be determined';
        $data = [
            'foundingDirector' => $foundingDirector,
            'letterHead' => $letterHead,
            'educationManager' => $educationManager,
            'monthYear' => $date,
            'year' => $year ,  // Retrieve and assign the required data
            'month' => $month ,  // Retrieve and assign the required data
            'monthName' => $monthName ,  // Retrieve and assign the required data
    ];




        view()->share('educationalreports::reports.pdf.itqan.all',$data);
        $pdf = PDF::loadView('educationalreports::reports.pdf.itqan.all', $data);
        // Generate a filename that includes month and year for easier identification
        $fileName = "ITQAN_Report_{$monthName}_{$year}.pdf";


        // Return the downloadable PDF
        return $pdf->download($fileName);


        
    }

}
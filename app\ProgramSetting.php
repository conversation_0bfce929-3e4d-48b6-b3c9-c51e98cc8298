<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ProgramSetting extends Model
{
        
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'program_settings';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['program_id', 'setting' , 'value'];

    public function program(){
        return $this->belongsTo('App\Program');
    }

}

<?php

namespace Modules\ExaminationCertification\Http\Controllers;

use App\BaseSetup;
use App\Employee;
use App\Student;
use App\StudentAttendance;
use App\StudentHefzPlan;
use App\StudentHefzReport;
use App\Subject;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Matrix\Builder;
use Session;
use App\Center;

use App\Classes;
use App\Program;
use Carbon\Carbon;
use App\ClassStudent;
use App\ClassTeacher;
use App\Http\Requests;
use App\ClassTeacherSubject;
use Illuminate\Http\Request;
use App\ClassSubjectTimetable;
use App\Http\Controllers\Controller;
use App\Cen_Emp;
use Tests\Psalm\LaravelPlugin\Models\Car;

class SubjectClassController extends Controller
{


    public function addSubjectToClass(Request $request)
    {


        try {





            $classes = Classes::whereIn('id',$request->get('classesId'))->update(['subject_id' => $request->get('subject_id')]);


//            DB::beginTransaction();

            return response()->json([

                'message' => 'Class(es) assigned to the subject'
            ], 201);



//            $programId = DB::table('class_programs')->where('class_id', $request->get('class_id'))->first()->program_id;
//
//            $classTeacherSubject = ClassTeacherSubject::updateOrCreate([
//
//                'class_teacher_id' => $request->class_teacher_id
//
//            ],[
//
//                'subject_id' => $request->subject_id,
//            'start_date' => Carbon::now(),
//            'program_id' => $programId
//            ]);
//
//
//
//            DB::commit();
//
//            Session::flash('flash_message', 'Teacher was added Succesfully!');
//
//            return redirect()->back();

        } catch (\Exception $e) {
            \Log::error($e);
            DB::rollBack();
            dd($e->getMessage());
            Toastr::error('Operation Failed', 'Failed');
            return redirect()->back();
        }


    }


}

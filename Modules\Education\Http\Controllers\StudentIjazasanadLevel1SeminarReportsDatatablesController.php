<?php

namespace Modules\Education\Http\Controllers;


use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\ProgramLevelLesson;
use App\StudentIjazasanadMemorizationReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;


class StudentIjazasanadLevel1SeminarReportsDatatablesController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function studentRecords(Request $request)
    {

        DB::connection()->enableQueryLog();

        if ($request->filled('studentId') || $request->filled('classDate')) {

            $planYearMonth = Carbon::parse($request->get('classDate'))->format('Y-m');


            $dateMonthArray =
            $year = Carbon::parse($request->get('classDate'))->year;
            $month = Carbon::parse($request->get('classDate'))->month;



            $StudentIjazasanadMemorizationReport = StudentIjazasanadMemorizationReport::where('student_id', $request->get('studentId'))
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->whereHas('ijazasanadMemorizationPlan.program_level.lessons', function ($query) {
                    $query->where('properties->course', 'ijazasanadseminars'); // Match "course" key with value "ijazasanadtalqeen"
                })
                ->with('ijazasanadMemorizationPlan')
                ->orderBy('created_at', 'asc')
                ->get();




            return \Yajra\DataTables\DataTables::of($StudentIjazasanadMemorizationReport)
                ->addIndexColumn()
                ->addColumn('day', function ($reportDetails) use ($request) {

                    $day = $reportDetails->created_at->format('l');
                    $shortDay = substr($day, 0, 3);// this will return the first three letters of the $day. 0 is the starting point in the string and 3 represent the number of chars to show/extract from the string after 0 offset

                    return '<span title="' . $shortDay . '" style="color: #b4eeb0;">' . $shortDay . '</span>';


                    return $shortDay;


                })
                ->addColumn('date', function ($reportDetails) use ($request) {

                    $stReportShowRoute = route('reports.create', ['id' => $reportDetails->class_id, 'from_date' => $reportDetails->created_at->toDateString()]);

                    return '<a style="color:#b4eeb0; padding: 2px; display: inline-block; transition: all 0.3s ease;" data-id='.$reportDetails->id.' class="section classReportDatelink" target="_blank" href="' . $stReportShowRoute . '">' . $reportDetails->created_at->format('d/m/Y') . '</a>';



                    return $reportDetails->created_at->format('d/m/Y');




                })
                ->addColumn('from_lesson', function ($reportDetails) use ($request) {

                    $lesson = ProgramLevelLesson::where('id', $reportDetails->seminars_from_lesson)
                        ->whereJsonContains('properties->course', 'ijazasanadseminars')
                        ->first();
                    $lessonTitle = $lesson ? $lesson->properties['lessonName'] : 'N/A';

                    return '<span style="color: #b4eeb0;">' . $lessonTitle . '</span>';
                })
                ->addColumn('to_lesson', function ($reportDetails) use ($request) {
                    $lesson = ProgramLevelLesson::where('id', $reportDetails->seminars_to_lesson)
                        ->whereJsonContains('properties->course', 'ijazasanadseminars')
                        ->first();
                    $lessonTitle = $lesson ? $lesson->properties['lessonName'] : 'N/A';

                    return '<span style="color: #b4eeb0;">' . $lessonTitle . '</span>';                })
                ->addColumn('no_of_lessons', function ($reportDetails) use ($request) {
                    $noOfLessons = $reportDetails->seminars_to_lesson - $reportDetails->seminars_from_lesson;
                    return '<span style="color: #b4eeb0;">' . $noOfLessons . '</span>';
                })
                ->addColumn('grade', function ($reportDetails) use ($request) {
//                    $evaluationTitle = EvaluationSchemaOption::where('id', $reportDetails->ijazasanad_evaluation_id)->first()->title;

                    $evaluationTitle = EvaluationSchemaOption::where('id', $reportDetails->ijazasanad_evaluation_id)->first()->title;

                    return '<span style="color: #b4eeb0;">' . $evaluationTitle . '</span>';
                    return $evaluationTitle;

                    return $evaluationTitle;


                })
                ->addColumn('attendance', function ($reportDetails) use ($request) {

                    // Check the attendance status based on the attendance_id
                    switch ($reportDetails->attendance_id) {
                        case 1:  // Late
                        case 2:  // On Time
                            // If attendance_id is 1 (Late) or 2 (On Time), return 'Y'
                            return '<span style="color: #b4eeb0;">Y</span>';

                        case 3:  // Absent
                        case 4:  // Excused
                            // If attendance_id is 3 (Absent) or 4 (Excused), return 'N'
                            return '<span style="color: #e74c3c;">N</span>';

                        default:
                            // Default case for Not Applicable or unknown attendance_id
                            return '<span style="color: #b4eeb0;">N/A</span>';
                    }



                })
                ->rawColumns(['from_lesson','to_lesson','attendance','grade','date','day','DT_RowIndex','no_of_lessons'])

                ->make(true);

        }

        dd('only ajax requests are allowed');


    }


}

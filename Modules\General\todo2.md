

**Revision Plan System Enhancements**

**Objective:** Modify 'Revision Plan' management. Implement row-specific actions in the DataTable (revisionPlanDataTable) and improve the batch approval process.

**Task 1: Implement Row-Specific Actions in revisionPlanDataTable**

1. **Modify DataTable Frontend (JavaScript):**  
   * Target the DataTable with ID revisionPlanDataTable.  
   * Add a new column to this DataTable.  
   * Modify the Laravel controller \`RevisionPlanDatatablesController@getPlansNeedApproval\` to include the 'Approve', 'Edit', and 'Comment' actions directly within the student name column of the DataTable. Instead of adding a new column for actions, generate HTML buttons for each action ('Approve', 'Edit', 'Comment') and append them *under* the student's name within the data being returned for the \`name\` column. Ensure each button includes necessary data attributes (e.g., student ID) to facilitate subsequent actions.  
   * Ensure these buttons operate on the specific student in their respective row.  
2. **Configure Button Actions (JavaScript \- AJAX):**  
   * **'Approve' Button:**  
     * On click, trigger an AJAX POST request to an appropriate backend endpoint (this might be a new individual approval endpoint or reuse the batch endpoint logic for a single item – see Task 2).  
     * Pass the specific student's ID and any other necessary data.  
     * Handle the AJAX response using Toastr notifications (success/error).  
   * **'Edit' Button:**  
     * On click, redirect to an edit page : reuse the existing editing functionality..  
   * **'Comment' Button ( it is already there in the datatble backend with column name of actions but you need to remove it from ther becuase it goes with the group under the student name):**  
     * On click, trigger a comment modal: reuse the existing one \+ add more functionality if you need.  
     * If using AJAX, define the endpoint and expected response. Specify if this requires a new route and controller method or reuse the existing one that is used for .  
     * Pass the specific student's ID.

**Task 2: Enhance Batch Approval Logic and Feedback**

1. **Modify Backend Controller (Laravel 10):**  
   * Target the controller method associated with the general.dashboard.approve_revision_plan route. (You may need to create a new method or refactor the existing one).  
   * **Individual Processing:** The method must process each student submitted in a batch individually. Do not reject the entire batch if one student fails.  
   * **Success Handling:** Approve and save valid students.  
   * **Error Handling:** For students that fail approval, collect their identifiers (e.g., student id, full\_name) and the specific reason for failure.

2. **Update Frontend Batch Approval Feedback (JavaScript \- AJAX & Toastr):**  
   * Modify the JavaScript function that handles the AJAX response from the batch approval.  
   * Parse the JSON response from the backend.  
   * Use the existing Toastr notification system:  
     * Display a success message indicating the number of students successfully approved (e.g., "19 out of 20 students approved.").  
     * If problematic\_students array is not empty, display a clear error message listing the students that failed and their respective errors. This information is for the user to share with IT technicians (e.g., "Failed to approve: Student X (ID: 10\) \- Error: Invalid plan details.").  
     * Refresh or update the DataTable as needed to reflect the changes.


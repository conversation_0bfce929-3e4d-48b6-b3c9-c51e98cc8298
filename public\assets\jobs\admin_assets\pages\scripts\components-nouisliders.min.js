var ComponentsNoUiSliders=function(){var e=function(){var e=document.getElementById("demo2");noUiSlider.create(e,{start:[20],connect:!1,range:{min:0,max:100}})},n=function(){var e=document.getElementById("demo3");noUiSlider.create(e,{start:[20,80],connect:!1,range:{min:0,max:100}});var n=document.createElement("div"),t=e.getElementsByClassName("noUi-base")[0],i=e.getElementsByClassName("noUi-origin");n.className+="connect",t.appendChild(n),e.noUiSlider.on("update",function(e,t){var o=t?"right":"left",a=i[t].style.left.slice(0,-1);1===t&&(a=100-a),n.style[o]=a+"%"})},t=function(){for(var e=document.getElementById("demo4_select"),n=-20;40>=n;n++){var t=document.createElement("option");t.text=n,t.value=n,e.appendChild(t)}var i=document.getElementById("demo4");noUiSlider.create(i,{start:[10,30],connect:!0,range:{min:-20,max:40}});var o=document.getElementById("demo4_input");i.noUiSlider.on("update",function(n,t){var i=n[t];t?o.value=i:e.value=Math.round(i)}),e.addEventListener("change",function(){i.noUiSlider.set([this.value,null])}),o.addEventListener("change",function(){i.noUiSlider.set([null,this.value])})},i=function(){function e(e){return e.parentElement.style.left}var n=document.getElementById("demo5");noUiSlider.create(n,{connect:!0,behaviour:"tap",start:[500,4e3],range:{min:[0],"10%":[500,500],"50%":[4e3,1e3],max:[1e4]}});var t=document.getElementById("demo5_lower-value"),i=document.getElementById("demo5_upper-value"),o=n.getElementsByClassName("noUi-handle");n.noUiSlider.on("update",function(n,a){a?i.innerHTML=n[a]+", "+e(o[a]):t.innerHTML=n[a]+", "+e(o[a])})},o=function(){function e(e,n){if(t){var a=o===n?0:1,d=a?0:1;e-=i[d]-i[a],n.noUiSlider.set(e)}}function n(){i=[Number(o.noUiSlider.get()),Number(a.noUiSlider.get())]}var t=!1,i=[60,80],o=document.getElementById("demo6_slider1"),a=document.getElementById("demo6_slider2"),d=document.getElementById("demo6_lockbutton"),r=document.getElementById("demo6_slider1-span"),l=document.getElementById("demo6_slider2-span");d.addEventListener("click",function(){t=!t,this.textContent=t?"unlock":"lock"}),noUiSlider.create(o,{start:60,animate:!1,range:{min:50,max:100}}),noUiSlider.create(a,{start:80,animate:!1,range:{min:50,max:100}}),o.noUiSlider.on("update",function(e,n){r.innerHTML=e[n]}),a.noUiSlider.on("update",function(e,n){l.innerHTML=e[n]}),o.noUiSlider.on("change",n),a.noUiSlider.on("change",n),o.noUiSlider.on("slide",function(n,t){e(n[t],a)}),a.noUiSlider.on("slide",function(n,t){e(n[t],o)})},a=function(){var e=document.getElementById("demo7");noUiSlider.create(e,{start:50,range:{min:0,max:100},pips:{mode:"values",values:[20,80],density:4}}),e.noUiSlider.on("change",function(n,t){n[t]<20?e.noUiSlider.set(20):n[t]>80&&e.noUiSlider.set(80)})},d=function(){var e=document.getElementById("demo8");noUiSlider.create(e,{start:[40,50],connect:!0,range:{min:30,"30%":40,max:50}});for(var n=e.getElementsByClassName("noUi-handle"),t=[],i=0;i<n.length;i++)t[i]=document.createElement("div"),n[i].appendChild(t[i]);t[1].className+="noUi-tooltip",t[1].innerHTML="<strong>Value: </strong><span></span>",t[1]=t[1].getElementsByTagName("span")[0],t[0].className+="noUi-tooltip",t[0].innerHTML="<strong>Value: </strong><span></span>",t[0]=t[0].getElementsByTagName("span")[0],e.noUiSlider.on("update",function(e,n){t[n].innerHTML=e[n]})};return{init:function(){e(),n(),t(),i(),o(),a(),d()}}}();jQuery(document).ready(function(){ComponentsNoUiSliders.init()});
<?php

namespace App\Handlers;

class LfmConfigHandler extends \UniSharp\LaravelFilemanager\Handlers\ConfigHandler
{
    public function userField()
    {


        if(auth()->user()->hasRole(['human-resource_2_', 'finance_2_','website-editor_2_','it-officer_2_','receptionist_2_','education-manager_2_','managing-director_2_'])){
            return '';
        }else{
            return '/' . auth()->id();
        }


        return parent::userField();
    }
}

<?php

namespace Modules\Admission\Http\Requests;

use App\Rules\StudentStudyDirectionSuratRule;
use Illuminate\Foundation\Http\FormRequest;

class CreateHefzPlanRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
//        dd($this->all());
        return [
            "hefz.start_from_surat" => "required",
            "hefz.start_from_ayat" => "required",
            "hefz.to_surat" => "required",
            "hefz.to_ayat" => "required",
            "hefz.study_direction" => ['required', new StudentStudyDirectionSuratRule($this->all())],
            "hefz.num_to_memorize" => "required",
            "hefz.memorization_mood" => "required",
            "hefz.pages_to_revise" => "required",
            "hefz.start_date" => "required",
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
}

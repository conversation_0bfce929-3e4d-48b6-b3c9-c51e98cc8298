var EcommerceOrders=function(){var e=function(){$(".date-picker").datepicker({rtl:App.isRTL(),autoclose:!0})},a=function(){var e=new Datatable;e.init({src:$("#datatable_orders"),onSuccess:function(e){},onError:function(e){},loadingMessage:"Loading...",dataTable:{lengthMenu:[[10,20,50,100,150,-1],[10,20,50,100,150,"All"]],pageLength:10,ajax:{url:"../demo/ecommerce_orders.php"},order:[[1,"asc"]]}}),e.getTableWrapper().on("click",".table-group-action-submit",function(a){a.preventDefault();var t=$(".table-group-action-input",e.getTableWrapper());""!=t.val()&&e.getSelectedRowsCount()>0?(e.setAjaxParam("customActionType","group_action"),e.setAjaxParam("customActionName",t.val()),e.setAjaxParam("id",e.getSelectedRows()),e.getDataTable().ajax.reload(),e.clearAjaxParams()):""==t.val()?alert({type:"danger",icon:"warning",message:"Please select an action",container:e.getTableWrapper(),place:"prepend"}):0===e.getSelectedRowsCount()&&alert({type:"danger",icon:"warning",message:"No record selected",container:e.getTableWrapper(),place:"prepend"})})};return{init:function(){e(),a()}}}();jQuery(document).ready(function(){EcommerceOrders.init()});
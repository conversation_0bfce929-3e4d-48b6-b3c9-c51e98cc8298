<?php

namespace App\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Package;
use App\Role;
use App\Permission;
use Illuminate\Http\Request;
use Session;

class PackagesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        $perPage = 25;

        if (!empty($keyword)) {
            $packages = Package::where('name', 'LIKE', "%$keyword%")
                ->where('organization_id' , '=' , 0 )
				->orWhere('content', 'LIKE', "%$keyword%")
				->paginate($perPage);
        } else {
            $packages = Package::paginate($perPage);
        }
        return view('packages.index', compact('packages'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('packages.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        // return $request;



        $this->validate($request, [
            'role' => 'unique:roles,name|required|min:3|max:32|alpha_num'
        ]);

        $role = Role::create(['name' => \Illuminate\Support\Str::slug($request->role),
                      'guard_name' => 'employee',
                      'organization_id' => 0
                        ]);

        $package = new Package;

        $package->role_id = $role->id;
        $package->monthly_price = $request->monthly_price;
        $package->yearly_price = $request->yearly_price;
        $package->order = $request->order;;
        $package->status = $request->status;;

        foreach ($request->translate as $code => $translate) {
            $package->translateOrNew($code)->title = $translate['title'];
            $package->translateOrNew($code)->description = $translate['description'];
        }

        $package->save();

        $this->update_permissions($role->id, $request);


        Session::flash('flash_message', 'Package added!');

        return redirect(route('superior.packages.index'));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $package = Role::findOrFail($id);

        return view('packages.show', compact('package'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $package = Package::findOrFail($id);

        $role = Role::findOrFail($package->role_id);

        return view('packages.edit', compact('package' , 'role'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {
        // return $request->all();
        if(!auth()->guard('superior')->check()){
            flash('Unauthorized access');
            return redirect()->back();
        }



        // $this->validate($request, [
        //     'role' => 'unique:roles|required|min:3|max:32|alpha_num'
        // ]);


        
        $package = Package::findOrFail($id);
        
        $role = Role::findOrFail($package->role_id);

        $package->role_id = $role->id;
        $package->monthly_price = $request->monthly_price;
        $package->yearly_price = $request->yearly_price;
        $package->order = $request->order;;
        $package->status = $request->status;;

        foreach ($request->translate as $code => $translate) {
            $package->translateOrNew($code)->title = $translate['title'];
            $package->translateOrNew($code)->description = $translate['description'];
        }

        $package->save();

        $this->update_permissions($role->id, $request);

        Session::flash('flash_message', 'Package updated!');

        return redirect()->route('superior.packages.index');
    }

    private function update_permissions($id, Request $request)
    {

        
        $requestData = $request->all();
        $role = Role::findOrFail($id);
        $permissions = [];

        // $role->revokePermissionTo(Permission::all());
        
        foreach($request->permissions as $permission){
            $get_permission = Permission::where('name' , '=' , $permission)
                                        ->where('guard_name' ,'=' , 'employee')
                                        ->get();
            if(!count($get_permission)){
                $get_permission = Permission::create([
                        'name' => $permission,
                        'guard_name' => 'employee',
                        'organization_id' => 0
                        ]);
                
            }
            $permissions[] = $permission;

            // $role->givePermissionTo($permission);

        }
        $role->syncPermissions($permissions);
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        Role::destroy($id);

        Session::flash('flash_message', 'Role deleted!');

        return redirect('packages');
    }
}

/** FOOTABLE MINMAL SKIN 
*************************************************** **/
.footable.fooMinimal {
	border:0;
}
table.fooMinimal {
    font-size: 13px;
	font-weight:400;
}
table.fooMinimal td {
    padding: 3px 15px !important;
    vertical-align: top !important;
    border-top: 1px solid #efefef !important;
    line-height: 1.4 !important;
    text-align: right !important;
}
table.fooMinimal td.foo-cell {
    text-align: left !important;
    padding-left: 0px !important;
}
table.fooMinimal th {
  background:#fff !important;
  border-right:0 !important;
  border-left:0 !important;
}
table.fooMinimal th.foo-cell {
  text-align: left;
}
table.fooMinimal th:first-child {
    padding-left: 0;
}
table.fooMinimal td, .g-mobile-table {
    font-size: 14px;
    font-weight: 300;
    color: #555;
}
table.fooMinimal td {
    padding: 12px 15px;
    vertical-align: top;
    border-top: 1px solid #efefef;
    line-height: 1.4;
}
table.fooMinimal td:first-child {
    padding-left: 0;
}
table.fooMinimal tr:first-child td {
    
}
table.fooMinimal th:first-child {
    padding-left: 0;
}
table.fooMinimal th {
    vertical-align: bottom;
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    padding: 5px 15px 8px 15px;
}
table.fooMinimal td.highlight {
  background: #e1f1f6 !important;
  border-top: 1px solid white !important;
}
html:not(.viewport-medium-10) table.fooMinimal th {
  font-size: 13px;
  padding: 4px 15px 4px 15px;
  text-align: right;
  padding-right: 0px;
}
html:not(.viewport-medium-10) table.fooMinimal th:first-child {
	text-align: left;
}
html:not(.viewport-medium-10) table.fooMinimal td  {
  font-size: 13px;
  padding: 2px 0px;
}
table.fooMinimal .footable-row-detail-name {
	text-align: left;
}
table.fooMinimal th {
  border-bottom: 1px solid #ccc;
}
table.fooMinimal.g-fixed-header th {
  padding-top: 30px;
}
table.fooMinimal th {
  position: relative;
  border-bottom: 1px solid #ccc;
  padding-bottom: 6px;
  vertical-align: bottom;
}
<?php

namespace Modules\Education\Http\Controllers;


use App\Classes;
use App\EvaluationSchemaOption;
use App\MoshafSurah;
use App\Student;
use App\StudentHefzReport;
use App\StudentRevisionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class HalaqahMonthYearController extends Controller
{
    
    /**
     * Display a listing of the resource.
     * @return Response
     */

    public function getMonthYears($classId)
    {


        // find out the program of the class.
        // then check if it is nouranya program, then load the nouranya. if  ijazasanad, then iajazasand. if memorization and revision, then memorizatin and revision.




        try {
            // Retrieve the class with its associated programs
            $class = Classes::with('programs')->findOrFail($classId);

            // For simplicity, we assume a class has at least one program
            $program = $class->programs->first();

            if (!$program) {
                return response()->json([
                    'error' => 'No program associated with this class.'
                ], 404);
            }

            // Get the program title
            $programTitle = $program->title;

            // Determine which report model to use based on the program title
            // Determine the report model based on the program title
            if (stripos($programTitle, 'nuraniyah') !== false || stripos($programTitle, 'nouranya') !== false) {
                $reportModel = \App\StudentNouranyaReport::class;
                $reportType  = 'StudentNouranyaReport';
            } elseif (stripos($programTitle, 'ijazah and sanad') !== false) {
                $reportModel = \App\StudentIjazasanadMemorizationReport::class;
                $reportType  = 'StudentIjazasanadMemorizationReport';
            } elseif (strtolower($programTitle) === 'memorization and revision') {
                $reportModel = \App\StudentHefzReport::class;
                $reportType  = 'StudentHefzReport';
            } else {
                return response()->json([
                    'error' => 'No matching report type found for the given program title.'
                ], 404);
            }

            // Execute the query with an identical structure for both report models
            $dates = $reportModel::where('class_id', $classId)
                ->selectRaw('MONTHNAME(created_at) as month, YEAR(created_at) as year')
                ->groupBy('year', 'month')
                ->orderByDesc('year')
                ->orderByRaw("FIELD(month, 'December', 'November', 'October', 'September', 'August', 'July', 'June', 'May', 'April', 'March', 'February', 'January')")
                ->get();


            return response()->json([
                'report_type' => $reportType,
                'data'        => $dates,
            ], 200);

        } catch (\Exception $e) {
            Log::error("Error retrieving report for class ID {$classId}: " . $e->getMessage());
            return response()->json([
                'error' => 'An error occurred while retrieving the report.'
            ], 500);
        }

    }
}

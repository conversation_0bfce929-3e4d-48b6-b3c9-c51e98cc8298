<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services\Ai;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modu<PERSON>\JobSeeker\Entities\JobSeeker;
use Modules\JobSeeker\Entities\JobSeekerResume;

/**
 * AiResumeService manages resume storage, retrieval, and lifecycle for AI tailoring.
 * 
 * Purpose: Centralize resume operations with deduplication, limits, and multi-source support.
 * Operations: List, store files, import LinkedIn, record voice transcripts, delete with cleanup.
 * Business rules: Max 5 per user; SHA256 deduplication; secure file storage; comprehensive logging.
 * Sources: 'upload' (files), 'linkedin' (text), 'voice' (transcripts).
 * Security: Non-public file storage; MIME validation; size limits; user ownership checks.
 * Performance: Efficient queries; batch operations; proper indexing utilization.
 */
final class AiResumeService
{
    private const MAX_RESUMES_PER_USER = 5;
    private const RESUME_STORAGE_DISK = 'local';
    private const RESUME_STORAGE_PATH = 'jobseeker/resumes';

    /**
     * Get all resumes for a specific job seeker, ordered by creation date (newest first)
     * 
     * @param int $jobSeekerId The job seeker's ID
     * @return Collection<JobSeekerResume> Collection of user's resumes
     */
    public function listForUser(int $jobSeekerId): Collection
    {
        Log::debug('AiResumeService: Fetching resumes for user', [
            'job_seeker_id' => $jobSeekerId,
        ]);

        $resumes = JobSeekerResume::forJobSeeker($jobSeekerId)->get();

        Log::info('AiResumeService: Retrieved resumes for user', [
            'job_seeker_id' => $jobSeekerId,
            'resume_count' => $resumes->count(),
        ]);

        return $resumes;
    }

    /**
     * Store an uploaded file as a resume with deduplication and limit enforcement
     * 
     * @param JobSeeker $user The job seeker uploading the resume
     * @param UploadedFile $file The uploaded resume file
     * @param string $title User-provided title for the resume
     * @return JobSeekerResume The created resume record
     * @throws \Exception If user has reached limit or file processing fails
     */
    public function storeUploadedFile(JobSeeker $user, UploadedFile $file, string $title): JobSeekerResume
    {
        $correlationId = 'resume_upload_' . Str::uuid();

        Log::info('AiResumeService: Starting file upload processing', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'title' => $title,
            'original_name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime' => $file->getMimeType(),
        ]);

        try {
            // Check user limit before processing
            $this->enforceUserLimit($user->id);

            // Generate content hash for deduplication
            $fileContent = $file->get();
            $contentHash = JobSeekerResume::generateContentHash($fileContent);

            // Check for duplicate
            $existingResume = JobSeekerResume::where('job_seeker_id', $user->id)
                ->where('sha256_hash', $contentHash)
                ->first();

            if ($existingResume) {
                Log::warning('AiResumeService: Duplicate file detected', [
                    'correlation_id' => $correlationId,
                    'job_seeker_id' => $user->id,
                    'existing_resume_id' => $existingResume->id,
                    'existing_title' => $existingResume->title,
                ]);

                throw new \Exception("A resume with identical content already exists: '{$existingResume->title}'");
            }

            // Generate unique storage path
            $extension = $file->getClientOriginalExtension();
            $fileName = Str::uuid() . '.' . $extension;
            $storagePath = self::RESUME_STORAGE_PATH . '/' . $user->id . '/' . $fileName;

            // Store file securely
            $stored = Storage::disk(self::RESUME_STORAGE_DISK)->put($storagePath, $fileContent);

            if (!$stored) {
                throw new \Exception('Failed to store resume file');
            }

            // Create database record
            $resume = JobSeekerResume::create([
                'job_seeker_id' => $user->id,
                'title' => $title,
                'source' => 'upload',
                'storage_path' => $storagePath,
                'mime' => $file->getMimeType(),
                'size_bytes' => $file->getSize(),
                'content_text' => null, // Files don't store text content
                'sha256_hash' => $contentHash,
            ]);

            Log::info('AiResumeService: Successfully stored uploaded resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_id' => $resume->id,
                'storage_path' => $storagePath,
                'size_bytes' => $file->getSize(),
            ]);

            return $resume;

        } catch (\Exception $e) {
            Log::error('AiResumeService: Failed to store uploaded file', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Clean up stored file if database creation failed
            if (isset($storagePath)) {
                Storage::disk(self::RESUME_STORAGE_DISK)->delete($storagePath);
            }

            throw $e;
        }
    }

    /**
     * Store LinkedIn profile content as a text-based resume
     * 
     * @param JobSeeker $user The job seeker importing the content
     * @param string $title User-provided title for the resume
     * @param string $contentText The LinkedIn profile content
     * @param string|null $linkedinUrl Optional LinkedIn URL for reference
     * @return JobSeekerResume The created resume record
     * @throws \Exception If user has reached limit or content processing fails
     */
    public function storeLinkedIn(JobSeeker $user, string $title, string $contentText, ?string $linkedinUrl = null): JobSeekerResume
    {
        $correlationId = 'linkedin_import_' . Str::uuid();

        Log::info('AiResumeService: Starting LinkedIn import processing', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'title' => $title,
            'content_length' => strlen($contentText),
            'has_url' => !empty($linkedinUrl),
        ]);

        try {
            // Check user limit before processing
            $this->enforceUserLimit($user->id);

            // Sanitize and clean content
            $cleanContent = $this->sanitizeTextContent($contentText);

            // Generate content hash for deduplication
            $contentHash = JobSeekerResume::generateContentHash($cleanContent);

            // Check for duplicate
            $existingResume = JobSeekerResume::where('job_seeker_id', $user->id)
                ->where('sha256_hash', $contentHash)
                ->first();

            if ($existingResume) {
                Log::warning('AiResumeService: Duplicate LinkedIn content detected', [
                    'correlation_id' => $correlationId,
                    'job_seeker_id' => $user->id,
                    'existing_resume_id' => $existingResume->id,
                    'existing_title' => $existingResume->title,
                ]);

                throw new \Exception("A resume with identical content already exists: '{$existingResume->title}'");
            }

            // Add LinkedIn URL to content if provided
            if (!empty($linkedinUrl)) {
                $cleanContent .= "\n\nLinkedIn Profile: " . $linkedinUrl;
            }

            // Create database record
            $resume = JobSeekerResume::create([
                'job_seeker_id' => $user->id,
                'title' => $title,
                'source' => 'linkedin',
                'storage_path' => null, // LinkedIn content is stored as text
                'mime' => 'text/plain',
                'size_bytes' => strlen($cleanContent),
                'content_text' => $cleanContent,
                'sha256_hash' => $contentHash,
            ]);

            Log::info('AiResumeService: Successfully stored LinkedIn resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_id' => $resume->id,
                'content_length' => strlen($cleanContent),
            ]);

            return $resume;

        } catch (\Exception $e) {
            Log::error('AiResumeService: Failed to store LinkedIn import', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Store voice transcript as a text-based resume
     * 
     * @param JobSeeker $user The job seeker recording the resume
     * @param string $title User-provided title for the resume
     * @param string $transcriptText The voice transcript content
     * @param int|null $durationSeconds Optional recording duration for metadata
     * @return JobSeekerResume The created resume record
     * @throws \Exception If user has reached limit or content processing fails
     */
    public function storeVoiceTranscript(JobSeeker $user, string $title, string $transcriptText, ?int $durationSeconds = null): JobSeekerResume
    {
        $correlationId = 'voice_transcript_' . Str::uuid();

        Log::info('AiResumeService: Starting voice transcript processing', [
            'correlation_id' => $correlationId,
            'job_seeker_id' => $user->id,
            'title' => $title,
            'transcript_length' => strlen($transcriptText),
            'duration_seconds' => $durationSeconds,
        ]);

        try {
            // Check user limit before processing
            $this->enforceUserLimit($user->id);

            // Clean and enhance transcript
            $cleanTranscript = $this->sanitizeTextContent($transcriptText);
            
            // Add metadata to transcript
            $enhancedContent = "Voice Resume Transcript\n";
            $enhancedContent .= "Recorded: " . now()->format('Y-m-d H:i:s') . "\n";
            if ($durationSeconds) {
                $enhancedContent .= "Duration: " . $this->formatDuration($durationSeconds) . "\n";
            }
            $enhancedContent .= "\n" . $cleanTranscript;

            // Generate content hash for deduplication
            $contentHash = JobSeekerResume::generateContentHash($cleanTranscript); // Hash the clean content, not metadata

            // Check for duplicate
            $existingResume = JobSeekerResume::where('job_seeker_id', $user->id)
                ->where('sha256_hash', $contentHash)
                ->first();

            if ($existingResume) {
                Log::warning('AiResumeService: Duplicate voice transcript detected', [
                    'correlation_id' => $correlationId,
                    'job_seeker_id' => $user->id,
                    'existing_resume_id' => $existingResume->id,
                    'existing_title' => $existingResume->title,
                ]);

                throw new \Exception("A resume with identical content already exists: '{$existingResume->title}'");
            }

            // Create database record
            $resume = JobSeekerResume::create([
                'job_seeker_id' => $user->id,
                'title' => $title,
                'source' => 'voice',
                'storage_path' => null, // Voice content is stored as text
                'mime' => 'text/plain',
                'size_bytes' => strlen($enhancedContent),
                'content_text' => $enhancedContent,
                'sha256_hash' => $contentHash,
            ]);

            Log::info('AiResumeService: Successfully stored voice transcript resume', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'resume_id' => $resume->id,
                'content_length' => strlen($enhancedContent),
                'duration_seconds' => $durationSeconds,
            ]);

            return $resume;

        } catch (\Exception $e) {
            Log::error('AiResumeService: Failed to store voice transcript', [
                'correlation_id' => $correlationId,
                'job_seeker_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Delete a resume and clean up associated files
     * 
     * @param JobSeekerResume $resume The resume to delete
     * @param int $userId The requesting user's ID (for ownership verification)
     * @throws \Exception If user doesn't own the resume or deletion fails
     */
    public function deleteResume(JobSeekerResume $resume, int $userId): void
    {
        $correlationId = 'resume_delete_' . Str::uuid();

        Log::info('AiResumeService: Starting resume deletion', [
            'correlation_id' => $correlationId,
            'resume_id' => $resume->id,
            'job_seeker_id' => $resume->job_seeker_id,
            'requesting_user_id' => $userId,
            'title' => $resume->title,
            'source' => $resume->source,
        ]);

        try {
            // Verify ownership
            if ($resume->job_seeker_id !== $userId) {
                throw new \Exception('Cannot delete resume: not owned by requesting user');
            }

            // Check if resume is currently being used in any active tailor runs
            $activeRuns = $resume->tailorRuns()
                ->whereIn('status', ['pending', 'running'])
                ->count();

            if ($activeRuns > 0) {
                Log::warning('AiResumeService: Cannot delete resume with active tailor runs', [
                    'correlation_id' => $correlationId,
                    'resume_id' => $resume->id,
                    'active_runs' => $activeRuns,
                ]);

                throw new \Exception('Cannot delete resume: it is currently being used in an active AI tailoring process');
            }

            // Delete the resume (file cleanup handled by model's boot method)
            $resume->delete();

            Log::info('AiResumeService: Successfully deleted resume', [
                'correlation_id' => $correlationId,
                'resume_id' => $resume->id,
                'job_seeker_id' => $userId,
            ]);

        } catch (\Exception $e) {
            Log::error('AiResumeService: Failed to delete resume', [
                'correlation_id' => $correlationId,
                'resume_id' => $resume->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Get current resume count for a user
     * 
     * @param int $jobSeekerId The job seeker's ID
     * @return int Current number of resumes
     */
    public function getResumeCount(int $jobSeekerId): int
    {
        return JobSeekerResume::countForJobSeeker($jobSeekerId);
    }

    /**
     * Check if user can add more resumes
     * 
     * @param int $jobSeekerId The job seeker's ID
     * @return bool True if user can add more resumes
     */
    public function canAddMore(int $jobSeekerId): bool
    {
        return $this->getResumeCount($jobSeekerId) < self::MAX_RESUMES_PER_USER;
    }

    /**
     * Enforce the per-user resume limit
     * 
     * @param int $jobSeekerId The job seeker's ID
     * @throws \Exception If user has reached the limit
     */
    private function enforceUserLimit(int $jobSeekerId): void
    {
        $currentCount = $this->getResumeCount($jobSeekerId);

        if ($currentCount >= self::MAX_RESUMES_PER_USER) {
            throw new \Exception("Resume limit reached. You can only store up to {self::MAX_RESUMES_PER_USER} resumes. Please delete an existing resume before adding a new one.");
        }

        Log::debug('AiResumeService: User limit check passed', [
            'job_seeker_id' => $jobSeekerId,
            'current_count' => $currentCount,
            'limit' => self::MAX_RESUMES_PER_USER,
        ]);
    }

    /**
     * Sanitize text content to prevent XSS and clean up formatting
     * 
     * @param string $content The raw text content
     * @return string Clean, safe text content
     */
    private function sanitizeTextContent(string $content): string
    {
        // Strip HTML tags
        $clean = strip_tags($content);

        // Normalize whitespace
        $clean = preg_replace('/\s+/', ' ', $clean);
        $clean = preg_replace('/\n\s*\n/', "\n\n", $clean);

        // Trim and limit length
        $clean = trim($clean);

        // Remove potentially dangerous characters while preserving readability
        $clean = preg_replace('/[^\p{L}\p{N}\p{P}\p{Z}\p{S}\n]/u', '', $clean);

        return $clean;
    }

    /**
     * Format duration in seconds to human-readable string
     * 
     * @param int $seconds Duration in seconds
     * @return string Formatted duration (e.g., "2m 30s")
     */
    private function formatDuration(int $seconds): string
    {
        if ($seconds < 60) {
            return $seconds . 's';
        }

        $minutes = floor($seconds / 60);
        $remainingSeconds = $seconds % 60;

        if ($remainingSeconds === 0) {
            return $minutes . 'm';
        }

        return $minutes . 'm ' . $remainingSeconds . 's';
    }
}

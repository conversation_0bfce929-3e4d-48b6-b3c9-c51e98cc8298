<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Astrotomic\Translatable\Translatable;
use App\Scopes\OrganizationScope;
use Illuminate\Database\Eloquent\SoftDeletes;

class Program extends Model
{
    use Translatable,SoftDeletes;

    public $translatedAttributes = array('description', 'title');

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'programs';

    /**
     * The database primary key value.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['code', 'status', 'language', 'require_interview', 'organization_id','minimum_mark_to_pass'];


    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope(new OrganizationScope);
    }
    public function evaluationSchemas()
    {
        return $this->hasMany(EvaluationSchema::class);
    }
    public function levels()
    {
        return $this->hasMany(ProgramLevel::class)->with('arabic')->orderBy('program_level_order');
    }

    public function centers()
    {
        return $this->belongsToMany(Center::class, 'center_programs', 'program_id', 'center_id');
    }

    public function classes()
    {
        return $this->belongsToMany(Classes::class, 'class_programs', 'program_id', 'class_id');
    }

    public function settings()
    {
        return $this->hasOne(ProgramSetting::class);
    }

    public function getSettingAttribute()
    {
        return $this->settings()->get()->pluck('value', 'setting');
    }

    public function programTranslations()
    {
//        return $this->hasMany('App\ProgramTranslation');
        return $this->hasMany('App\ProgramTranslation')->where("locale",'en');
    }
}

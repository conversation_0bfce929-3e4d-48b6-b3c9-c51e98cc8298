<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

final class LanguageDetectionService
{
    /**
     * Determine if a title should be treated as English for filtering purposes.
     * Policy is configurable via config('jobseeker.language_detection').
     */
    public function isEnglishTitle(string $text, ?string $providerName = null): bool
    {
        $text = trim($text);
        if ($text === '') {
            return false;
        }

        // Resolve policy: provider override -> global
        $mixedAsEnglish = null;
        $maxRtlRatio = null;
        if ($providerName) {
            $setting = \Modules\JobSeeker\Entities\ProviderSetting::forProvider($providerName);
            if ($setting) {
                $mixedAsEnglish = $setting->mixed_as_english;
                $maxRtlRatio = $setting->max_rtl_ratio;
            }
        }
        if ($mixedAsEnglish === null) {
            $config = (array) config('jobseeker.language_detection', []);
            $mixedAsEnglish = (bool) ($config['mixed_as_english'] ?? true);
            $maxRtlRatio = (float) ($maxRtlRatio ?? ($config['max_rtl_ratio'] ?? 0.0));
        }

        // RTL ranges: Arabic, Persian/Dari, Pashto and related blocks
        $rtlPattern = '/[\x{0600}-\x{06FF}\x{0750}-\x{077F}\x{08A0}-\x{08FF}\x{FB50}-\x{FDFF}\x{FE70}-\x{FEFF}\x{10E60}-\x{10E7F}]/u';
        $latinPattern = '/[A-Za-z]/u';

        $hasLatin = preg_match($latinPattern, $text) === 1;
        $hasRtl = preg_match($rtlPattern, $text) === 1;

        // Fast paths
        if ($mixedAsEnglish) {
            // Any Latin letter qualifies as English regardless of accompanying RTL characters
            return $hasLatin;
        }

        if (!$hasRtl) {
            // No RTL chars: if there is any Latin char, treat as English; otherwise false
            return $hasLatin;
        }

        // Ratio-based policy: compute proportion of RTL codepoints among letters
        $lettersOnly = preg_replace('/[^\p{L}]+/u', '', $text) ?? '';
        if ($lettersOnly === '') {
            return false;
        }

        $rtlCount = preg_match_all($rtlPattern, $lettersOnly) ?: 0;
        $latinCount = preg_match_all($latinPattern, $lettersOnly) ?: 0;
        $totalLetters = $rtlCount + $latinCount;
        if ($totalLetters === 0) {
            return false;
        }

        $rtlRatio = $rtlCount / $totalLetters;
        return $latinCount > 0 && $rtlRatio <= (float) $maxRtlRatio;
    }
}



<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class JobSeekerPersonalContact extends Model
{
    protected $table = 'job_seeker_personal_contacts';
    
    protected $fillable = [
        'job_seeker_id',
        'recipient_email_id',
        'display_name',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'job_seeker_id' => 'integer',
        'recipient_email_id' => 'integer',
    ];

    /**
     * Get the job seeker that owns this personal contact.
     */
    public function jobSeeker(): BelongsTo
    {
        return $this->belongsTo(JobSeeker::class, 'job_seeker_id');
    }

    /**
     * Get the recipient email record for this personal contact.
     */
    public function recipientEmail(): BelongsTo
    {
        return $this->belongsTo(JobNotificationRecipientEmail::class, 'recipient_email_id');
    }
} 
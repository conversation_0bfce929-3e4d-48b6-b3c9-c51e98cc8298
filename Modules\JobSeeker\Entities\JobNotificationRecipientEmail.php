<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class JobNotificationRecipientEmail extends Model
{
    protected $table = 'job_notification_recipient_emails';
    
    protected $fillable = [
        'email',
        'name',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the notification recipients that use this email.
     */
    public function recipients(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(JobNotificationRecipient::class, 'recipient_email_id');
    }

    /**
     * Get the personal contact links for this email.
     */
    public function personalContactLinks(): Has<PERSON>any
    {
        return $this->hasMany(JobSeekerPersonalContact::class, 'recipient_email_id');
    }
} 
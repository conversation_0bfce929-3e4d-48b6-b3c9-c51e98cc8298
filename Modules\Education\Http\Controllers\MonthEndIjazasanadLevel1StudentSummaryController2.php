<?php

namespace Modules\Education\Http\Controllers;



use App\ClassTimetable;
use App\ProgramLevelLesson;
use App\StudentIjazasanadMemorizationReport;
use App\StudentIjazasanadRevisionReport;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class MonthEndIjazasanadLevel1StudentSummaryController2 extends Controller
{


    /**
     * @param Request $request
     * @param $classId
     * @return Application|Factory|View|\Illuminate\Foundation\Application|JsonResponse|\Illuminate\View\View
     */
    public function __invoke(Request $request, $classId)
    {

        DB::connection()->enableQueryLog();
        try {
            $planYearMonth = Carbon::parse($request->get('classDate'));

            $year = $planYearMonth->year;
            $month = $planYearMonth->month;


            // Fetch records based on student_id and class_id from the request
            $studentId = $request->get('studentId');

            $memorizationReports = \App\StudentIjazasanadMemorizationReport::where('student_id', $studentId)
                ->whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->with('ijazasanadMemorizationPlan')

                ->get();

            $totalPlannedTalqeen = $memorizationReports->sum(function ($report) {
                return $report->ijazasanadMemorizationPlan ? $report->ijazasanadMemorizationPlan->talqeen_to_lesson - $report->ijazasanadMemorizationPlan->talqeen_from_lesson : 0;
            });

            $totalPlannedJazariyah = $memorizationReports->sum(function ($report) {
                return $report->ijazasanadMemorizationPlan ? $report->ijazasanadMemorizationPlan->jazariyah_to_lesson - $report->ijazasanadMemorizationPlan->jazariyah_from_lesson : 0;
            });

            $totalPlannedSeminars = $memorizationReports->sum(function ($report) {
                return $report->ijazasanadMemorizationPlan ? $report->ijazasanadMemorizationPlan->seminars_to_lesson - $report->ijazasanadMemorizationPlan->seminars_from_lesson : 0;
            });

            $totalPlannedRevisions = $memorizationReports->sum(function ($report) {
                return $report->ijazasanadMemorizationPlan ? $report->ijazasanadMemorizationPlan->revision_to_lesson - $report->ijazasanadMemorizationPlan->revision_from_lesson : 0;
            });

            // Calculate achieved lessons for each category and fetch lesson details
            $totalAchievedTalqeen = $memorizationReports->sum(function ($report) {
                return $report->talqeen_to_lesson - $report->talqeen_from_lesson;
            });

            $totalAchievedJazariyah = $memorizationReports->sum(function ($report) {
                return $report->jazariyah_to_lesson - $report->jazariyah_from_lesson;
            });

            $totalAchievedSeminars = $memorizationReports->sum(function ($report) {
                return $report->seminars_to_lesson - $report->seminars_from_lesson;
            });

            $totalAchievedRevisions = $memorizationReports->sum(function ($report) {
                return $report->revision_to_lesson - $report->revision_from_lesson;
            });

            // Fetch lesson details for each report type

            $seminarReportDetails = $memorizationReports->map(function ($report) {
                $firstLesson = \App\ProgramLevelLesson::where('id', '>=', (int)$report->seminars_from_lesson)
                    ->orderBy('id', 'asc')
                    ->first();
                $lastLesson = \App\ProgramLevelLesson::where('id', '<=', (int)$report->seminars_to_lesson)
                    ->orderBy('id', 'desc')
                    ->first();
                return $firstLesson && $lastLesson ? "<span style='color: #b4eeb0;'>{$firstLesson->properties['no']} ({$firstLesson->properties['lessonName']}) to {$lastLesson->properties['no']} ({$lastLesson->properties['lessonName']})</span>" : 'N/A';
            })->first();

            $talqeenReportDetails = $memorizationReports->map(function ($report) {
                $firstLesson = \App\ProgramLevelLesson::where('id', '>=', (int)$report->talqeen_from_lesson)
                    ->whereJsonContains('properties->course', 'ijazasanadtalqeen')
                    ->orderBy('id', 'asc')
                    ->first();
                $lastLesson = \App\ProgramLevelLesson::where('id', '<=', (int)$report->talqeen_to_lesson)
                    ->whereJsonContains('properties->course', 'ijazasanadtalqeen')
                    ->orderBy('id', 'desc')
                    ->first();
                return $firstLesson && $lastLesson ? "<span style='color: #b4eeb0;'>{$firstLesson->properties['no']} ({$firstLesson->properties['fromVerse']} to {$firstLesson->properties['toVerse']}) to {$lastLesson->properties['no']} ({$lastLesson->properties['fromVerse']} to {$lastLesson->properties['toVerse']})</span>" : 'N/A';
            })->first();


            $jazariyahReportDetails = $memorizationReports->map(function ($report) {
                $firstLesson = \App\ProgramLevelLesson::where('id', '>=', (int)$report->jazariyah_from_lesson)
                    ->orderBy('id', 'asc')
                    ->first();
                $lastLesson = \App\ProgramLevelLesson::where('id', '<=', (int)$report->jazariyah_to_lesson)
                    ->orderBy('id', 'desc')
                    ->first();
                return $firstLesson && $lastLesson ? "<span style='color: #b4eeb0;'>{$firstLesson->properties['no']} ({$firstLesson->properties['jazariyah']}) to {$lastLesson->properties['no']} ({$lastLesson->properties['jazariyah']})</span>" : 'N/A';
            })->first();
            $revisionReportDetails = $memorizationReports->map(function ($report) {
                $firstLesson = \App\ProgramLevelLesson::where('id', '>=', (int)$report->revision_from_lesson)
                    ->orderBy('id', 'asc')
                    ->first();
                $lastLesson = \App\ProgramLevelLesson::where('id', '<=', (int)$report->revision_to_lesson)
                    ->orderBy('id', 'desc')
                    ->first();
                return $firstLesson && $lastLesson ? "<span style='color: #b4eeb0;'>{$firstLesson->properties['no']} ({$firstLesson->properties['juzu']}) to {$lastLesson->properties['no']} ({$lastLesson->properties['juzu']})</span>" : 'N/A';
            })->first();


            // Calculate percentages for each category
            $talqeenPercentage = $totalPlannedTalqeen > 0 ? ($totalAchievedTalqeen / $totalPlannedTalqeen) * 100 : 0;
            $jazariyahPercentage = $totalPlannedJazariyah > 0 ? ($totalAchievedJazariyah / $totalPlannedJazariyah) * 100 : 0;
            $seminarPercentage = $totalPlannedSeminars > 0 ? ($totalAchievedSeminars / $totalPlannedSeminars) * 100 : 0;
            $revisionPercentage = $totalPlannedRevisions > 0 ? ($totalAchievedRevisions / $totalPlannedRevisions) * 100 : 0;

            // Calculate average achieved percentage
            $averageAchieved = ($talqeenPercentage + $jazariyahPercentage + $seminarPercentage + $revisionPercentage) / 4;

            $classTimetable = \App\ClassTimetable::where('class_id', $classId)->first();
            $totalClasses = 0;


            if ($classTimetable) {
                $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

                // Iterate through each day of the month to check for scheduled classes
                for ($day = 1; $day <= $daysInMonth; $day++) {
                    $dayOfWeek = strtolower(date('D', strtotime("$year-$month-$day")));

                    // Increase totalClasses if the class is scheduled on this day
                    if (!is_null($classTimetable->$dayOfWeek)) {
                        $totalClasses++;
                    }
                }
            }


            // Calculate total attendance days (number of attended classes) from the reports
            $totalAttendance = $memorizationReports->whereIn('attendance_id', [1, 2])->count();

            // Calculate attendance percentage based on total scheduled classes
            $attendancePercentage = $totalClasses > 0 ? ($totalAttendance / $totalClasses) * 100 : 0;

            // Prepare data to pass to the view
            $data = [
                'revision_report'       => $revisionReportDetails,
                'talqeen_report'        => $talqeenReportDetails,
                'jazariyah_report'      => $jazariyahReportDetails,
                'seminar_report'        => $seminarReportDetails,
                'revision_percentage'   => "<span style='color: #b4eeb0;'>" . round($revisionPercentage, 2) . '%</span>',
                'talqeen_percentage'    => "<span style='color: #b4eeb0;'>" . round($talqeenPercentage, 2) . '%</span>',
                'jazariyah_percentage'  => "<span style='color: #b4eeb0;'>" . round($jazariyahPercentage, 2) . '%</span>',
                'seminar_percentage'    => "<span style='color: #b4eeb0;'>" . round($seminarPercentage, 2) . '%</span>',
                'average_achieved'      => "<span style='color: #b4eeb0;'>" . round($averageAchieved, 2) . '%</span>',
                'attendance_percentage' => "<span style='color: #b4eeb0;'>" . round($attendancePercentage, 2) . '%</span>',
            ];


            return DataTables::of(collect([$data]))
                ->rawColumns(['attendance_percentage','revision_report', 'talqeen_report', 'jazariyah_report', 'seminar_report', 'revision_percentage', 'talqeen_percentage', 'jazariyah_percentage', 'seminar_percentage', 'average_achieved'])
                ->make(true);


        }catch (\Exception $e){
            dd($e->getMessage());
        }
    }






}

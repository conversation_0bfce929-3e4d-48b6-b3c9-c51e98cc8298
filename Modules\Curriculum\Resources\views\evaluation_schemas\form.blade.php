<div class="form-group {{ $errors->has('title') ? 'has-error' : ''}}">
    {!! Form::label('title', 'Evaluation title', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::text('title'  , null, ['class' => 'form-control']) !!}
        {!! $errors->first('title', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group {{ $errors->has('description') ? 'has-error' : ''}}">
    {!! Form::label('description', 'Evaluation description', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::textarea('description'  , null, ['class' => 'form-control']) !!}
        {!! $errors->first('description', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group {{ $errors->has('target') ? 'has-error' : ''}}">
    {!! Form::label('target', 'target', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('target', ['subject' => 'Subjects' ,'content' => 'Contents'] , null, ['class' => 'form-control']) !!}
        {!! $errors->first('target', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group {{ $errors->has('type') ? 'has-error' : ''}}">
    {!! Form::label('type', 'type', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('type', ['marks' => 'Marks' , 'grades' => 'Grades'] , null, ['class' => 'form-control']) !!}
        {!! $errors->first('type', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<div id="evaluation_options" class="container table-bordered pa-20 mb-10">
    <div class="clearfix">
        <h4 class="pull-left">Evaluation Options</h4>
        <button class="pull-right btn btn-primary" onclick="addEvaluationOption()" type="button">Add Option</button>
    </div>
    @isset($evaluation_schema)
    @foreach($evaluation_schema->options as $option)
        
        <div class="row">
            <div class="col-sm-2">
                <label for="code">Option Code/Value </label>
                <div class="form-control"> {{ $option->code }} </div>
            </div>
            <div class="col-sm-3">
                <label for="title">Option Title </label>
                <div class="form-control"> {{ $option->title }} </div>
            </div>
            <div class="col-sm-5">
                <label for="description">Option Description</label>
                <div class="form-control">  {{ $option->description }} </div>
            </div>
            <div class="col-sm-2">
                <br>
                <button type="button" class="btn btn-danger ">Delete</button>
            </div>
        </div>
    @endforeach
    @endisset
    {{--  @foreach()  --}}


</div>
<div class="form-group {{ $errors->has('status') ? 'has-error' : ''}}">
    {!! Form::label('status', 'Status', ['class' => 'col-md-4 control-label']) !!}
    <div class="col-md-6">
        {!! Form::select('status', [1 => 'active'] , null, ['class' => 'form-control']) !!}
        {!! $errors->first('status', '<p class="help-block">:message</p>') !!}
    </div>
</div>

<div class="form-group">
    <div class="col-md-offset-4 col-md-4">
        {!! Form::submit(isset($submitButtonText) ? $submitButtonText : trans('common.create'), ['class' => 'btn btn-primary']) !!}
    </div>
</div>


@section('js')
<script>
    var num_rows=0;
    $(document).ready(function() {
        checkSchemaType();

        $('#type').change(function(){
            checkSchemaType();
        });
        
    });

    var checkSchemaType = function(){
        if($('#type').val() == 'marks'){
            $('#evaluation_options').hide();
        }else{
            $('#evaluation_options').show();            
        }
    } 

    var addEvaluationOption = function(){
        num_rows++;
        var option = '<div class="row" id="option-row-'+num_rows+'">'
                    +'<div class="col-sm-2">'
                        +'<label for="code">Option Code/Value </label>'
                        +'<input class="form-control" type="text" name="options[new'+num_rows+'][code]" id="code" value="">'
                    +'</div>'
                    +'<div class="col-sm-3">'
                        +'<label for="title">Option Title </label>'
                        +'<input class="form-control" type="text" name="options[new'+num_rows+'][title]" id="title" value="">'
                    +'</div>'
                    +'<div class="col-sm-5">'
                        +'<label for="description">Option Description</label>'
                        +'<input class="form-control" type="text" name="options[new'+num_rows+'][description]" id="description" value="">'
                    +'</div>'
                    +'<div class="col-sm-2">'
                        +'<br>'
                        +'<button type="button" class="btn btn-danger " onclick="$(\'#option-row-'+num_rows+'\').remove()">Delete</button>'
                    +'</div>'
                +'</div>';
        $('#evaluation_options').append(option);
    }
</script>
@endsection

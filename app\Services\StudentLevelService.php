<?php
declare(strict_types=1);

namespace App\Services;

use App\Admission;
use App\Program;
use App\ProgramLevel;
use App\Student;
use App\StudentProgramLevel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Exception;

final class StudentLevelService
{
    /**
     * Assigns the initial level to a student based on the program type during admission or transfer.
     *
     * @param Student $student
     * @param int $classId
     * @param Program $program
     * @return StudentProgramLevel|null
     * @throws Exception
     */
    public function assignInitialLevel(Student $student, int $classId, Program $program): ?StudentProgramLevel
    {
        Log::info('Attempting to assign initial level', ['student_id' => $student->id, 'class_id' => $classId, 'program_id' => $program->id]);

        $levelId = $this->determineInitialLevelId($program);

        if ($levelId === null) {
            Log::warning('Could not determine initial level ID for program', ['program_id' => $program->id, 'program_title' => $program->title]);
            // Decide if we should throw an exception or return null/false
            // For now, let's log and return null, assuming some programs might not have initial levels defined this way.
            return null;
        }

        try {
            // Use DB transaction?
            $studentProgramLevel = StudentProgramLevel::updateOrCreate(
                ['student_id' => $student->id, 'class_id' => $classId], // Use class_id for uniqueness per class context
                [
                    'level_id' => $levelId,
                    'status' => 'active' // Assuming 'active' is the default status
                    // 'organization_id' => config('organization_id') // Consider adding if needed globally
                ]
            );
            Log::info('Successfully assigned initial level', ['student_id' => $student->id, 'class_id' => $classId, 'level_id' => $levelId]);
            return $studentProgramLevel;
        } catch (Exception $e) {
            Log::error('Failed to assign initial level', ['student_id' => $student->id, 'class_id' => $classId, 'level_id' => $levelId, 'error' => $e->getMessage()]);
            throw new Exception('Failed to assign initial student level: ' . $e->getMessage());
        }
    }

    /**
     * Updates the level for a specific student in a specific class.
     *
     * @param int $studentId
     * @param int $classId
     * @param int $levelId
     * @return StudentProgramLevel
     * @throws Exception
     */
    public function updateLevel(int $studentId, int $classId, int $levelId): StudentProgramLevel
    {

       
        Log::info('Attempting to update student level', ['student_id' => $studentId, 'class_id' => $classId, 'new_level_id' => $levelId]);

        // Basic Validation: Check if level exists
        if (!ProgramLevel::where('id', $levelId)->exists()) {
            Log::error('Attempted to update to non-existent level', ['student_id' => $studentId, 'class_id' => $classId, 'level_id' => $levelId]);
            throw new Exception("Program Level with ID {$levelId} does not exist.");
        }

        // Add more validation here: e.g., check program compatibility, student eligibility

        try {
            // DB::transaction(function () use ($studentId, $classId, $levelId, &$studentProgramLevel) {
                // Soft delete any existing records that don't match the current student/class combination
                StudentProgramLevel::where('student_id', $studentId)
                    ->where('class_id', '!=', $classId)
                    ->delete();
                
                $studentProgramLevel = StudentProgramLevel::updateOrCreate(
                    ['student_id' => $studentId, 
                    'class_id' => $classId],
                    [
                        'level_id' => $levelId,
                        'class_id' => $classId,
                        'status' => 'active',
                        'deleted_at' => null // Ensure we're not restoring a soft-deleted record
                    ]
                );
            // });

            Log::info('Successfully updated student level', ['student_id' => $studentId, 'class_id' => $classId, 'level_id' => $levelId]);
            return $studentProgramLevel;
        } catch (Exception $e) {
            Log::error('Failed to update student level', ['student_id' => $studentId, 'class_id' => $classId, 'level_id' => $levelId, 'error' => $e->getMessage()]);
            // Consider wrapping in a custom exception type
            throw new Exception('Failed to update student level: ' . $e->getMessage());
        }
    }

    /**
     * Determines the initial level ID based on program title keywords.
     * Uses program_level_order to find appropriate initial levels for multi-tenant compatibility.
     *
     * @param Program $program
     * @return int|null
     */
    private function determineInitialLevelId(Program $program): ?int
    {
        Log::info('Determining initial level ID for program', [
            'program_id' => $program->id, 
            'program_title' => $program->title
        ]);
        
        $programTitleLower = strtolower($program->title);
        
        // Base query to find levels for this specific program
        $baseQuery = ProgramLevel::where('program_id', $program->id);
        
        // Default behavior - for most programs, we want the first level (lowest program_level_order)
        $defaultLevel = (clone $baseQuery)
            ->orderBy('program_level_order', 'asc')
            ->select('id')
            ->first();
        
        // Specific program type handling
        if (Str::contains($programTitleLower, ['memorization and revision', 'memorization & revision'])) {
            // For Memorization and Revision, we want the Preliminary Level (typically order 1)
            $level = (clone $baseQuery)
                ->whereHas('translations', function($query) {
                    $query->where('title', 'LIKE', '%Preliminary%')
                          ->orWhere('title', 'LIKE', '%تمهيدي%');
                })
                ->select('id')
                ->first();
                
            if ($level) {
                Log::info('Found Preliminary level for Memorization program', ['level_id' => $level->id]);
                return $level->id;
            }
        } 
        elseif (Str::contains($programTitleLower, ['nuraniyah', 'nouranya'])) {
            // For Nuraniyah programs, find the level with "Level 1" in the title
            $level = (clone $baseQuery)
                ->whereHas('translations', function($query) {
                    $query->where('title', 'LIKE', '%level 1%')
                          ->orWhere('title', 'LIKE', '%level1%')
                          ->orWhere('title', 'LIKE', '%المستوى الأول%');
                })
                ->select('id')
                ->first();
                
            if ($level) {
                Log::info('Found Level 1 for Nuraniyah program', ['level_id' => $level->id]);
                return $level->id;
            }
        }
        elseif (Str::contains($programTitleLower, ['ijazah and sanad'])) {
            // For Ijazah and Sanad, find the level with "Preparation Course" in the title
            $level = (clone $baseQuery)
                ->whereHas('translations', function($query) {
                    $query->where('title', 'LIKE', '%Preparation%');
                })
                ->select('id')
                ->first();
                
            if ($level) {
                Log::info('Found Preparation level for Ijazah program', ['level_id' => $level->id]);
                return $level->id;
            }
        }
        
        // If no specific level found, return the default first level
        if ($defaultLevel) {
            Log::info('Using default first level for program', [
                'program_id' => $program->id,
                'level_id' => $defaultLevel->id
            ]);
            return $defaultLevel->id;
        }
        
        Log::warning('No levels found for program', ['program_id' => $program->id]);
        return null;
    }
} 
<?php
namespace Modules\Leave\Database\Seeders;

use Illuminate\Database\Seeder;

class leave_typesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        \DB::table('leave_types')->insert([
            [
               'type'=>'Casual Leave',
               'total_days'=>10,
               'active_status'=>1,
               'organization_id'=>2,
            ],
            [
               'type'=>'Sick Leave',
               'total_days'=>14,
               'active_status'=>1,
                'organization_id'=>2,

            ], 
            [
               'type'=>'Annual/Vacation Leave',
               'total_days'=>10,
               'active_status'=>1,
                'organization_id'=>2,

            ],
            [
               'type'=>'Earned Leave',
               'total_days'=>10,
               'active_status'=>1,
                'organization_id'=>2,

            ], 
            [
               'type'=>'Public holidays',
               'total_days'=>20,
               'active_status'=>1,
                'organization_id'=>2,

            ],   
            [
               'type'=>'Maternity/Paternity',
               'total_days'=>7,
               'active_status'=>1,
                'organization_id'=>2,

            ], 
            [
               'type'=>'Administrative leave',
               'total_days'=>5,
               'active_status'=>1,
                'organization_id'=>2,

            ],  
        ]);
    }
}

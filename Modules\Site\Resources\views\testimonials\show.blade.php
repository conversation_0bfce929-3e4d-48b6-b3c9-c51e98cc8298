@extends('layouts.hound')

@section('content')
<div class="panel panel-default card-view">
    <div class="panel-heading"><h4>Testimonial {{ $testimonial->name }}</h4></div>
    <div class="panel-body">

        <a href="{{ url('/workplace/site/testimonials') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
        <a href="{{ url('/workplace/site/testimonials/' . $testimonial->id . '/edit') }}" title="Edit Testimonial"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
        {!! Form::open([
            'method'=>'DELETE',
            'url' => ['workplace/site/testimonials', $testimonial->id],
            'style' => 'display:inline'
        ]) !!}
            {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                    'type' => 'submit',
                    'class' => 'btn btn-danger btn-xs',
                    'title' => 'Delete Testimonial',
                    'onclick'=>'return confirm("Confirm delete?")'
            ))!!}
        {!! Form::close() !!}
        <br/>
        <br/>

        <div class="table-responsive">
            <table class="table table-borderless">
                <tbody>
                    <tr>
                        <th>ID</th><td>{{ $testimonial->id }}</td>
                    </tr>
                    <tr><th> Image </th><td> {{ $testimonial->image }} </td></tr><tr><th> Status </th><td> {{ $testimonial->status }} </td></tr>
                </tbody>
            </table>
        </div>

    </div>
</div>
@endsection

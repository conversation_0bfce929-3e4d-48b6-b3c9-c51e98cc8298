# Itqan Al Quran - Troubleshooting Guide

This guide covers common issues that developers may encounter while working with the Itqan Al Quran project and their solutions.

## Environment Setup Issues

### Composer Dependency Issues

**Issue**: Composer fails to install dependencies with version conflicts.

**Solution**:
1. Make sure you're using the correct PHP version (8.1+)
2. Try clearing composer cache:
   ```
   composer clearcache
   ```
3. If specific packages are causing conflicts, try:
   ```
   composer update package-name --with-dependencies
   ```
4. Check the `composer.json` file for any incorrect version constraints

### Database Connection Issues

**Issue**: Unable to connect to the database.

**Solution**:
1. Verify database credentials in `.env` file
2. Ensure MySQL service is running
3. Check if the database exists:
   ```
   mysql -u root -p -e "SHOW DATABASES;"
   ```
4. Try connecting directly using MySQL client:
   ```
   mysql -u root -p itqan
   ```
5. Check for firewall or networking issues if the database is on a remote server

### Missing View Error

**Issue**: `View [modules.modulename.viewname] not found`

**Solution**:
1. Remember that module views are located in `resources/views/modules/{module-name-lowercase}/` and NOT in the module's Resources directory
2. Check that the module's service provider is correctly registering views:
   ```php
   protected function registerViews()
   {
       $this->loadViewsFrom(resource_path('views/modules/module-name-lowercase'), 'module-name-lowercase');
   }
   ```
3. Verify the view exists in the correct location and with the correct name (case-sensitive)
4. Run `php artisan view:clear` to clear the view cache

## Module-Related Issues

### Module Not Found

**Issue**: `Module [ModuleName] not found!`

**Solution**:
1. Check that the module is registered in `modules_statuses.json` with a value of `true`
2. Verify that the module exists in the `Modules` directory with the correct case
3. Run `php artisan optimize:clear` to clear cached configurations
4. Ensure the module's service provider is correctly registered in `config/app.php`

### Module Route Not Found

**Issue**: `Route [modulename.route.name] not defined`

**Solution**:
1. Verify that the route is defined in the module's routes file (`Http/routes.php` or `Http/api.php`)
2. Check that the route name is spelled correctly (case-sensitive)
3. Make sure the module's routes are being loaded by the RouteServiceProvider
4. Run `php artisan route:list` to see all registered routes
5. Clear route cache: `php artisan route:clear`

## Database Issues

### Schema Change Errors

**Issue**: Errors when applying database schema changes.

**Solution**:
1. Check SQL syntax in your schema file
2. Verify that tables, columns, or indexes referenced in ALTER statements already exist
3. Ensure you have sufficient privileges on the database
4. Add proper error handling in SQL by using `IF EXISTS` clauses

### Eloquent Relationship Issues

**Issue**: `Property [relationship] does not exist on this collection instance`

**Solution**:
1. Verify the relationship is properly defined in the model
2. Check for typos in the relationship name
3. Ensure the related model exists and is correctly namespaced
4. Check that foreign keys follow Laravel's naming conventions or are explicitly defined

## Performance Issues

### Slow Queries

**Issue**: Database queries taking too long to execute.

**Solution**:
1. Use eager loading to prevent N+1 query problems:
   ```php
   $records = Model::with('relationship')->get();
   ```
2. Check for missing indexes on frequently queried columns
3. Use query debugging to analyze performance:
   ```php
   DB::enableQueryLog();
   // Run your query
   dd(DB::getQueryLog());
   ```
4. Consider caching results for queries that don't change often

### Memory Issues

**Issue**: PHP memory exhaustion errors.

**Solution**:
1. Increase memory limit in `php.ini` or using `ini_set('memory_limit', '512M')`
2. Process large datasets in chunks:
   ```php
   Model::chunk(100, function ($models) {
       foreach ($models as $model) {
           // Process model
       }
   });
   ```
3. Optimize database queries to return only needed columns
4. Implement pagination for large data sets

## Frontend Issues

### Asset Compilation Errors

**Issue**: Failed to compile frontend assets.

**Solution**:
1. Check for JavaScript or CSS syntax errors
2. Verify that all required npm packages are installed
3. Clear the compiled assets directory
4. Make sure you have sufficient disk space
5. Try running with increased memory: `NODE_OPTIONS=--max-old-space-size=4096 npm run dev`

### AJAX Request Failures

**Issue**: AJAX requests fail with 422 or 500 errors.

**Solution**:
1. Check browser console for detailed error messages
2. Verify that CSRF token is included in AJAX requests:
   ```javascript
   $.ajaxSetup({
       headers: {
           'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
       }
   });
   ```
3. Validate request data formats
4. Check server logs for errors
5. Use browser developer tools to inspect the network request/response cycle

## Common Laravel Error Messages

### Class Not Found

**Issue**: `Class 'Namespace\ClassName' not found`

**Solution**:
1. Make sure the class exists at the specified namespace
2. Check for typos in the class name or namespace
3. Run `composer dump-autoload` to refresh the autoloader
4. Verify that the proper use statements are included

### Method Not Found on Facade

**Issue**: `Call to undefined method Facade::method()`

**Solution**:
1. Verify that you're using the correct facade
2. Check that the method exists on the underlying class
3. Clear configuration cache: `php artisan config:clear`
4. Ensure proper facade aliases are registered in `config/app.php`

### Validation Errors

**Issue**: Request validation failing unexpectedly.

**Solution**:
1. Check the rules defined in your Form Request or validation call
2. Verify the incoming data structure matches what's expected
3. For debugging, display validation errors:
   ```php
   dd($validator->errors());
   ```
4. For custom validation rules, ensure they are properly registered

## Module-Specific Issues

### JobSeeker Module

**Issue**: Email notifications not being sent.

**Solution**:
1. Check mail configuration in `.env` file
2. Verify the email service provider is set up correctly
3. Check that the JobSeeker entity exists and is active
4. Look for errors in `storage/logs/laravel.log`
5. Test email configuration: `php artisan tinker` then `Mail::raw('Test', function($message) { $message->to('<EMAIL>')->subject('Test'); });`

### Education Module

**Issue**: Reports not generating correctly.

**Solution**:
1. Verify all required data is present in the database
2. Check permissions for the user trying to access reports
3. Look for any JavaScript errors in the browser console
4. Ensure report templates exist in the correct view location
5. Check for any recent changes in the report generation logic

## Advanced Troubleshooting

### Laravel Logging

Check Laravel logs for detailed error information:
```
tail -f storage/logs/laravel.log
```

### Database Query Logging

Enable query logging temporarily to debug database issues:
```php
DB::enableQueryLog();
// Your code here
dd(DB::getQueryLog());
```

### Using Telescope for Debugging

If Laravel Telescope is installed:
1. Access `/telescope` route in your development environment
2. Inspect requests, exceptions, queries, and more
3. Look for errors or performance bottlenecks

### PHP Configuration

View current PHP configuration:
```
php -i
```

### Request Debugging

Add debugging middleware to inspect requests:
```php
Route::middleware(function($request, $next) {
    logger()->debug('Request:', $request->all());
    return $next($request);
})->group(function() {
    // Your routes
});
```

## Getting Additional Help

If you encounter an issue not covered in this guide:
1. Check Laravel documentation: https://laravel.com/docs
2. Search for solutions on Laravel forums or Stack Overflow
3. Review project-specific documentation in `ai_docs` directory
4. Consult with team members who have worked on similar features
5. Create a detailed issue report with steps to reproduce the problem 
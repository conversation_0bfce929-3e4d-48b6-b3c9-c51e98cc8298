<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use <PERSON><PERSON><PERSON>\JobSeeker\Entities\EmailContentSetting;
use Mo<PERSON>les\JobSeeker\Entities\JobDetailedInfo;
use Modules\JobSeeker\Entities\Job;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * EmailContentManagerService
 * 
 * Manages email content settings and field rendering for job notification emails
 * Provides caching, field management, and formatting capabilities
 */
final class EmailContentManagerService
{
    private const CACHE_KEY_SETTINGS = 'email_content_settings';
    private const CACHE_KEY_ENABLED = 'email_content_enabled_fields';
    private const CACHE_TTL = 3600; // 1 hour

    /**
     * Get all active email content fields grouped and ordered
     */
    public function getActiveFields(): Collection
    {
        return Cache::remember(self::CACHE_KEY_SETTINGS, self::CACHE_TTL, function () {
            return EmailContentSetting::enabled()
                ->orderBy('display_order')
                ->orderBy('id')
                ->get();
        });
    }

    /**
     * Get enabled field names as array for quick lookup
     */
    public function getEnabledFieldNames(): array
    {
        return Cache::remember(self::CACHE_KEY_ENABLED, self::CACHE_TTL, function () {
            return EmailContentSetting::enabled()
                ->pluck('field_name')
                ->toArray();
        });
    }

    /**
     * Check if a specific field is enabled
     */
    public function isFieldEnabled(string $fieldName): bool
    {
        $enabledFields = $this->getEnabledFieldNames();
        return in_array($fieldName, $enabledFields, true);
    }

    /**
     * Get field groups organized for a specific job
     */
    public function getFieldGroupsForJob(array $job): Collection
    {
        $activeFields = $this->getActiveFields();
        
        return $activeFields->groupBy('field_group')
            ->map(function ($fields) use ($job) {
                return $fields->filter(function ($field) use ($job) {
                    return $this->shouldShowField($field, $job);
                });
            })
            ->filter(function ($fields) {
                return $fields->isNotEmpty();
            });
    }

    /**
     * Determine if field should be shown for this job
     */
    public function shouldShowField(EmailContentSetting $field, array $job): bool
    {
        if (!$field->is_enabled) {
            return false;
        }

        if ($field->conditional_display) {
            $value = $this->getJobFieldValue($field->field_name, $job);
            return !empty($value) && $value !== 'N/A' && $value !== null;
        }

        return true;
    }

    /**
     * Format field value according to settings
     */
    public function formatFieldValue(EmailContentSetting $field, array $job): string
    {
        $value = $this->getJobFieldValue($field->field_name, $job);

        if (empty($value)) {
            return $field->conditional_display ? '' : 'Not specified';
        }

        $formatting = $field->formatting_options ?? [];

        return $this->applyFormatting($value, $formatting, $field->field_name);
    }

    /**
     * Get job field value with fallbacks and detailed info integration
     */
    private function getJobFieldValue(string $fieldName, array $job): mixed
    {
        // Direct field access
        if (array_key_exists($fieldName, $job)) {
            return $job[$fieldName];
        }

        // Field mapping for common variations
        $fieldMappings = [
            'title' => ['title', 'position', 'job_title'],
            'company' => ['company', 'company_name'],
            'location' => ['location', 'locations'],
            'posted_date' => ['publish_date', 'posted_date', 'created_at'],
            'deadline' => ['expire_date', 'deadline', 'closing_date'],
        ];

        if (isset($fieldMappings[$fieldName])) {
            foreach ($fieldMappings[$fieldName] as $mapping) {
                if (array_key_exists($mapping, $job) && !empty($job[$mapping])) {
                    return $job[$mapping];
                }
            }
        }

        // Virtual field: setup_name uses setup context when present
        if ($fieldName === 'setup_name') {
            return $job['setup_name'] ?? null;
        }

        // Check detailed info if field requires provider fetch
        if ($this->requiresDetailedFetch($fieldName)) {
            return $this->getDetailedJobInfo($job['id'] ?? null, $fieldName);
        }

        return null;
    }

    /**
     * Apply formatting to field values
     */
    private function applyFormatting(mixed $value, array $formatting, string $fieldName): string
    {
        if (empty($value)) {
            return '';
        }

        // Convert to string for processing
        $stringValue = (string) $value;

        // Date formatting
        if (in_array($fieldName, ['publish_date', 'expire_date', 'posted_date', 'deadline'])) {
            return $this->formatDateField($value, $formatting);
        }

        // Text truncation and cleaning
        if (in_array($fieldName, ['description', 'about_company', 'job_requirements', 'duties_responsibilities'])) {
            return $this->formatTextualField($stringValue, $formatting);
        }

        // Currency formatting for salary
        if ($fieldName === 'salary') {
            return $this->formatSalaryField($stringValue, $formatting);
        }

        // Boolean fields
        if (in_array($fieldName, ['is_featured', 'can_apply_online', 'is_contract_extensible'])) {
            return $this->formatBooleanField($value);
        }

        // Numeric fields
        if (in_array($fieldName, ['number_of_vacancy', 'preferred_experience_years', 'applications_count', 'views_count'])) {
            return $this->formatNumericField($value);
        }

        // Apply general text truncation if specified
        if (isset($formatting['max_length']) && is_string($value)) {
            $maxLength = (int) $formatting['max_length'];
            if (strlen($stringValue) > $maxLength) {
                return substr($stringValue, 0, $maxLength) . '...';
            }
        }

        return $stringValue;
    }

    /**
     * Format date fields
     */
    private function formatDateField(mixed $value, array $formatting): string
    {
        try {
            $date = Carbon::parse($value);
            $format = $formatting['date_format'] ?? 'M j, Y';
            return $date->format($format);
        } catch (\Exception $e) {
            Log::warning('EmailContentManager: Failed to parse date', [
                'value' => $value,
                'error' => $e->getMessage()
            ]);
            return (string) $value;
        }
    }

    /**
     * Format textual fields with HTML cleaning and paragraph limits
     */
    private function formatTextualField(string $value, array $formatting): string
    {
        // Clean HTML tags and decode entities
        $cleaned = strip_tags($value);
        $cleaned = html_entity_decode($cleaned, ENT_QUOTES, 'UTF-8');

        // Apply paragraph limits for long text
        if (isset($formatting['max_paragraphs'])) {
            $paragraphs = explode("\n\n", $cleaned);
            $maxParagraphs = (int) $formatting['max_paragraphs'];
            if (count($paragraphs) > $maxParagraphs) {
                $cleaned = implode("\n\n", array_slice($paragraphs, 0, $maxParagraphs)) . "\n\n[...]";
            }
        }

        // Apply text truncation
        if (isset($formatting['max_length'])) {
            $maxLength = (int) $formatting['max_length'];
            if (strlen($cleaned) > $maxLength) {
                $cleaned = substr($cleaned, 0, $maxLength) . '...';
            }
        }

        // Clean up extra whitespace
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        return trim($cleaned);
    }

    /**
     * Format salary fields
     */
    private function formatSalaryField(string $value, array $formatting): string
    {
        if (isset($formatting['currency_symbol'])) {
            $symbol = $formatting['currency_symbol'];
            if (!str_starts_with($value, $symbol)) {
                return $symbol . ' ' . $value;
            }
        }

        return $value;
    }

    /**
     * Format boolean fields
     */
    private function formatBooleanField(mixed $value): string
    {
        if (is_bool($value)) {
            return $value ? 'Yes' : 'No';
        }

        if (is_numeric($value)) {
            return ((int) $value) === 1 ? 'Yes' : 'No';
        }

        if (is_string($value)) {
            $lower = strtolower($value);
            return in_array($lower, ['true', '1', 'yes', 'on']) ? 'Yes' : 'No';
        }

        return 'No';
    }

    /**
     * Format numeric fields
     */
    private function formatNumericField(mixed $value): string
    {
        if (is_numeric($value)) {
            $num = (int) $value;
            return $num > 0 ? number_format($num) : '0';
        }

        return (string) $value;
    }

    /**
     * Check if field requires detailed fetch from provider
     */
    private function requiresDetailedFetch(string $fieldName): bool
    {
        $detailedFields = [
            'detailed_description',
            'about_company',
            'duties_responsibilities',
            'job_requirements',
            'submission_guideline',
            'application_email',
            'application_website',
            'contact_person',
            'contact_phone',
            'benefits',
            'working_hours',
            'company_size',
            'industry_sector',
            'probation_period',
            'contract_duration',
            'minimum_education',
            'preferred_experience_years',
            'language_requirements',
            'additional_skills'
        ];

        return in_array($fieldName, $detailedFields, true);
    }

    /**
     * Get detailed job information from provider
     */
    private function getDetailedJobInfo(?int $jobId, string $fieldName): mixed
    {
        if (!$jobId) {
            return null;
        }

        try {
            $detailedInfo = JobDetailedInfo::where('job_id', $jobId)
                ->where('fetch_success', true)
                ->first();

            return $detailedInfo?->getAttribute($fieldName);
        } catch (\Exception $e) {
            Log::warning('EmailContentManager: Failed to get detailed job info', [
                'job_id' => $jobId,
                'field_name' => $fieldName,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get available fields for email content management
     */
    public function getAvailableFields(): array
    {
        return [
            EmailContentSetting::GROUP_BASIC => [
                'position' => 'Job Title/Position',
                'company_name' => 'Company Name',
                'locations' => 'Job Location',
                'source' => 'Job Provider',
                'publish_date' => 'Posted Date',
                'expire_date' => 'Application Deadline',
                'salary' => 'Salary Information',
                // Optional segment controller for headline/subhead (no data source required)
                'setup_name' => 'Setup Name (Headline Segment)',
                'is_featured' => 'Featured Job Badge',
                'number_of_vacancy' => 'Number of Positions',
                'vacancy_number' => 'Vacancy Number',
                'views_count' => 'Job Views',
                'applications_count' => 'Applications Count',
            ],
            EmailContentSetting::GROUP_COMPANY => [
                'company_logo' => 'Company Logo',
                'about_company' => 'About the Company',
                'company_profile_link' => 'Company Profile Link',
                'company_size' => 'Company Size',
                'industry_sector' => 'Industry/Sector',
            ],
            EmailContentSetting::GROUP_DETAILS => [
                'contract_type' => 'Contract Type',
                'work_type' => 'Work Type (Remote/Onsite)',
                'gender' => 'Gender Requirements',
                'working_hours' => 'Working Hours',
                'benefits' => 'Benefits & Perks',
                'probation_period' => 'Probation Period',
                'contract_duration' => 'Contract Duration',
                'is_contract_extensible' => 'Contract Extensible',
                'job_summary' => 'Job Summary',
                'description' => 'Job Description',
                'detailed_description' => 'Detailed Description',
            ],
            EmailContentSetting::GROUP_REQUIREMENTS => [
                'experience' => 'Experience Required',
                'preferred_experience_years' => 'Years of Experience',
                'minimum_education' => 'Education Requirements',
                'language_requirements' => 'Language Skills',
                'additional_skills' => 'Additional Skills',
                'job_requirements' => 'Detailed Requirements',
                'duties_responsibilities' => 'Duties & Responsibilities',
            ],
            EmailContentSetting::GROUP_APPLICATION => [
                'application_email' => 'Application Email',
                'application_website' => 'Application Website',
                'submission_guideline' => 'How to Apply',
                'contact_person' => 'Contact Person',
                'contact_phone' => 'Contact Phone',
                'can_apply_online' => 'Online Application Available',
            ],
        ];
    }

    /**
     * Get preset configurations for quick setup
     */
    public function getPresets(): array
    {
        return [
            'minimal' => [
                'name' => 'Minimal',
                'description' => 'Only essential job information',
                'enabled_fields' => [
                    'position', 'company_name', 'locations', 'publish_date', 'application_email'
                ]
            ],
            'standard' => [
                'name' => 'Standard',
                'description' => 'Balanced job information for most users',
                'enabled_fields' => [
                    'position', 'company_name', 'locations', 'source', 'publish_date', 
                    'salary', 'contract_type', 'work_type', 'experience', 
                    'application_email', 'submission_guideline'
                ]
            ],
            'detailed' => [
                'name' => 'Detailed',
                'description' => 'Comprehensive job information',
                'enabled_fields' => [
                    'position', 'company_name', 'company_logo', 'locations', 'source',
                    'publish_date', 'expire_date', 'salary', 'contract_type', 'work_type',
                    'gender', 'about_company', 'experience', 'job_requirements', 'benefits',
                    'application_email', 'application_website', 'submission_guideline',
                    'duties_responsibilities', 'number_of_vacancy'
                ]
            ]
        ];
    }

    /**
     * Get aggregation limits for emails (min/max jobs per email) from a pseudo-setting.
     * Uses EmailContentSetting with field_name = 'aggregation_prefs'.
     * When disabled (enabled = false), returns unlimited values to bypass aggregation.
     */
    public function getAggregationLimits(): array
    {
        $setting = EmailContentSetting::where('field_name', 'aggregation_prefs')->first();
        $defaults = ['enabled' => true, 'min_jobs_per_email' => 1, 'max_jobs_per_email' => 20];
        
        if (!$setting) {
            return $defaults;
        }
        
        $opts = $setting->formatting_options ?? [];
        $isEnabled = (bool)($opts['enabled'] ?? true);
        
        if (!$isEnabled) {
            // When disabled, return values that effectively bypass aggregation
            return [
                'enabled' => false,
                'min_jobs_per_email' => 0,
                'max_jobs_per_email' => PHP_INT_MAX  // Unlimited
            ];
        }
        
        $min = (int)($opts['min_jobs_per_email'] ?? $defaults['min_jobs_per_email']);
        $max = (int)($opts['max_jobs_per_email'] ?? $defaults['max_jobs_per_email']);
        if ($min < 1) { $min = 1; }
        if ($max < $min) { $max = $min; }
        
        return [
            'enabled' => true,
            'min_jobs_per_email' => $min, 
            'max_jobs_per_email' => $max
        ];
    }

    /**
     * Apply a preset configuration
     */
    public function applyPreset(string $presetName, string $updatedBy = 'system'): bool
    {
        $presets = $this->getPresets();
        
        if (!isset($presets[$presetName])) {
            Log::error('EmailContentManager: Invalid preset name', ['preset' => $presetName]);
            return false;
        }

        $preset = $presets[$presetName];
        
        try {
            // Disable all fields first
            EmailContentSetting::query()->update([
                'is_enabled' => false,
                'updated_by' => $updatedBy,
            ]);

            // Enable fields in the preset
            EmailContentSetting::whereIn('field_name', $preset['enabled_fields'])
                ->update([
                    'is_enabled' => true,
                    'updated_by' => $updatedBy,
                ]);

            $this->clearCache();

            Log::info('EmailContentManager: Preset applied successfully', [
                'preset' => $presetName,
                'enabled_fields_count' => count($preset['enabled_fields']),
                'updated_by' => $updatedBy
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('EmailContentManager: Failed to apply preset', [
                'preset' => $presetName,
                'error' => $e->getMessage(),
                'updated_by' => $updatedBy
            ]);
            return false;
        }
    }

    /**
     * Clear all caches
     */
    public function clearCache(): void
    {
        Cache::forget(self::CACHE_KEY_SETTINGS);
        Cache::forget(self::CACHE_KEY_ENABLED);
        EmailContentSetting::clearCache();
        
        Log::info('EmailContentManager: All caches cleared');
    }

    /**
     * Get sample job data for preview/testing - returns multiple jobs like real notifications
     * 
     * Purpose: Generate realistic sample data that matches the actual email notification
     * behavior where multiple jobs (typically 5-10) are sent per email, not just a single job.
     * This ensures the preview system accurately reflects what recipients will actually see.
     * 
     * @param int|null $count Number of sample jobs to generate (null = use aggregation max)
     * @return array Array of job data arrays
     */
    public function getSampleJobData(?int $count = null): array
    {
        // Use aggregation limits if count not specified
        if ($count === null) {
            $limits = $this->getAggregationLimits();
            $count = $limits['max_jobs_per_email'];
            
            // Cap sample data generation to prevent memory issues when aggregation is disabled
            if ($count > 50) {
                $count = 50; // Reasonable limit for sample data display
            }
        }

        $sampleJobs = [
            [
                'id' => 999999,
                'position' => 'Senior Software Developer',
                'company_name' => 'Tech Solutions Inc.',
                'locations' => 'Kabul, Afghanistan',
                'source' => 'Jobs.af',
                'publish_date' => '2024-01-15',
                'expire_date' => '2024-02-15',
                'salary' => 'AFN 50,000 - 70,000 monthly',
                'contract_type' => 'Full-time',
                'work_type' => 'Hybrid',
                'gender' => null,
                'experience' => '3-5 years',
                'number_of_vacancy' => 2,
                'is_featured' => false,
                'can_apply_online' => true,
                'slug' => 'senior-software-developer-tech-solutions',
            ],
            [
                'id' => 999998,
                'position' => 'Project Manager',
                'company_name' => 'Development Partners Ltd.',
                'locations' => 'Herat, Afghanistan',
                'source' => 'ACBAR',
                'publish_date' => '2024-01-16',
                'expire_date' => '2024-02-20',
                'salary' => 'AFN 60,000 - 80,000 monthly',
                'contract_type' => 'Full-time',
                'work_type' => 'On-site',
                'gender' => null,
                'experience' => '5+ years',
                'number_of_vacancy' => 1,
                'is_featured' => true,
                'can_apply_online' => true,
                'slug' => 'project-manager-development-partners',
            ],
            [
                'id' => 999997,
                'position' => 'Data Analyst',
                'company_name' => 'Analytics Corp',
                'locations' => 'Kabul, Afghanistan',
                'source' => 'Jobs.af',
                'publish_date' => '2024-01-14',
                'expire_date' => '2024-02-10',
                'salary' => 'AFN 40,000 - 55,000 monthly',
                'contract_type' => 'Full-time',
                'work_type' => 'Remote',
                'gender' => null,
                'experience' => '2-4 years',
                'number_of_vacancy' => 3,
                'is_featured' => false,
                'can_apply_online' => false,
                'slug' => 'data-analyst-analytics-corp',
            ],
            [
                'id' => 999996,
                'position' => 'Marketing Specialist',
                'company_name' => 'Creative Agency',
                'locations' => 'Mazar-i-Sharif, Afghanistan',
                'source' => 'ACBAR',
                'publish_date' => '2024-01-17',
                'expire_date' => '2024-02-25',
                'salary' => 'AFN 35,000 - 50,000 monthly',
                'contract_type' => 'Contract',
                'work_type' => 'Hybrid',
                'gender' => null,
                'experience' => '1-3 years',
                'number_of_vacancy' => 2,
                'is_featured' => false,
                'can_apply_online' => true,
                'slug' => 'marketing-specialist-creative-agency',
            ],
            [
                'id' => 999995,
                'position' => 'Financial Analyst',
                'company_name' => 'Finance Solutions',
                'locations' => 'Kabul, Afghanistan',
                'source' => 'Jobs.af',
                'publish_date' => '2024-01-13',
                'expire_date' => '2024-02-12',
                'salary' => 'AFN 45,000 - 65,000 monthly',
                'contract_type' => 'Full-time',
                'work_type' => 'On-site',
                'gender' => null,
                'experience' => '3-6 years',
                'number_of_vacancy' => 1,
                'is_featured' => true,
                'can_apply_online' => true,
                'slug' => 'financial-analyst-finance-solutions',
            ],
            [
                'id' => 999994,
                'position' => 'HR Coordinator',
                'company_name' => 'People First NGO',
                'locations' => 'Jalalabad, Afghanistan',
                'source' => 'ACBAR',
                'publish_date' => '2024-01-18',
                'expire_date' => '2024-02-28',
                'salary' => 'AFN 30,000 - 42,000 monthly',
                'contract_type' => 'Full-time',
                'work_type' => 'On-site',
                'gender' => 'Female preferred',
                'experience' => '2-4 years',
                'number_of_vacancy' => 1,
                'is_featured' => false,
                'can_apply_online' => false,
                'slug' => 'hr-coordinator-people-first-ngo',
            ],
            [
                'id' => 999993,
                'position' => 'Graphic Designer',
                'company_name' => 'Design Studio',
                'locations' => 'Kabul, Afghanistan',
                'source' => 'Jobs.af',
                'publish_date' => '2024-01-19',
                'expire_date' => '2024-03-01',
                'salary' => 'AFN 25,000 - 40,000 monthly',
                'contract_type' => 'Part-time',
                'work_type' => 'Remote',
                'gender' => null,
                'experience' => '1-2 years',
                'number_of_vacancy' => 2,
                'is_featured' => false,
                'can_apply_online' => true,
                'slug' => 'graphic-designer-design-studio',
            ],
            [
                'id' => 999992,
                'position' => 'Network Administrator',
                'company_name' => 'IT Services Inc.',
                'locations' => 'Kandahar, Afghanistan',
                'source' => 'Jobs.af',
                'publish_date' => '2024-01-12',
                'expire_date' => '2024-02-08',
                'salary' => 'AFN 55,000 - 75,000 monthly',
                'contract_type' => 'Full-time',
                'work_type' => 'On-site',
                'gender' => null,
                'experience' => '4-7 years',
                'number_of_vacancy' => 1,
                'is_featured' => true,
                'can_apply_online' => true,
                'slug' => 'network-administrator-it-services',
            ]
        ];

        // Return the requested number of jobs, cycling through if needed
        $result = [];
        for ($i = 0; $i < $count; $i++) {
            $result[] = $sampleJobs[$i % count($sampleJobs)];
        }

        return $result;
    }
}

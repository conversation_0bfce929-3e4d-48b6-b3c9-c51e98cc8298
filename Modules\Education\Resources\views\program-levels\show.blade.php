@extends('layouts.hound')
@section('mytitle', 'View Program Level')

@section('content')

    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="panel panel-default panel-flush">
                        <div class="panel-heading">
                                <div class="pull-left">
                                    Subjects 
                                </div>
                                <a class="btn btn-success btn-sm pull-right" data-toggle="modal" href='#editContent'> <i class="fa fa-plus" aria-hidden="true"></i> Add 
                                </a>

                               
                                <div class="clearfix"></div>
                            </div>
                    
            
                    <div class="panel-body">
                            <table class="table table-borderless">
                                    <tbody>
                                       @foreach ($sub_level->subjects as $item )
                                       <tr>
                                            <th>{{$item->title}}</th>
                                            <td> {!! Form::open([
                                                    'method'=>'DELETE',
                                                    'route' => ['program-levels.delete_program_level_subject', $item->id],
                                                    'style' => 'display:inline'
                                                ]) !!}
                                                    {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                                                            'type' => 'submit',
                                                            'class' => 'btn btn-danger btn-xs',
                                                            'title' => 'Delete ProgramLevel',
                                                            'onclick'=>'return confirm("Confirm delete?")'
                                                    ))!!}
                                                {!! Form::close() !!}</td>
                                        </tr>
                                       @endforeach
                                        
                                       
                                    </tbody>
                                </table>
                         
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <div class="panel panel-default">
                    <div class="panel-heading"> <h5> ProgramLevel {{ $programlevel->id }}</h5></div>
                    <div class="panel-body">

                        <a href="{{ url('/workplace/education/program-levels') }}" title="Back"><button class="btn btn-warning btn-xs"><i class="fa fa-arrow-left" aria-hidden="true"></i> Back</button></a>
                        <a href="{{ url('/workplace/education/program-levels/' . $programlevel->id . '/edit') }}" title="Edit ProgramLevel"><button class="btn btn-primary btn-xs"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit</button></a>
                        {!! Form::open([
                            'method'=>'DELETE',
                            'url' => ['workplace/education/programlevels', $programlevel->id],
                            'style' => 'display:inline'
                        ]) !!}
                            {!! Form::button('<i class="fa fa-trash-o" aria-hidden="true"></i> Delete', array(
                                    'type' => 'submit',
                                    'class' => 'btn btn-danger btn-xs',
                                    'title' => 'Delete ProgramLevel',
                                    'onclick'=>'return confirm("Confirm delete?")'
                            ))!!}
                        {!! Form::close() !!}
                        <br/>
                        <br/>

                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <th>ID</th><td>{{ $programlevel->id }}</td>
                                    </tr>
                                    <tr><th> Program Id </th><td> {{ $programlevel->program_id }} </td></tr><tr><th> Title </th><td> {{ $programlevel->title }} </td></tr><tr><th> Description </th><td> {{ $programlevel->description }} </td></tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" id="editContent" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="exampleModalLongTitle">Modal title</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
                
                    <form method="post" action="{{ route('program-levels.add_program_level_subject') }}">
              
                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                {!! Form::hidden('program_level_id' , $programlevel->id) !!}
                <table id="datable_1" class="table table-hover display  pb-30" >
                    <thead>
                        <tr>
                            <th>C</th>
                            <th>Title</th>
                            <th>Language</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th>C</th>
                            <th>Title</th>
                            <th>Language</th>
                        </tr>
                    </tfoot>
                    <tbody>
                        @foreach($subject as $subjects)
                        <tr>
                            <th>{!!Form::checkbox('subjects_id[]' , $subjects->id)!!}</th>
                            <th>{{ $subjects->title }}</th>
                            <th>{{ $subjects->language }}</th>
                          
                        </tr>
                        @endforeach
                    </tbody>
                </table>


            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
              {!! Form::submit(trans('common.save'), ['class' => 'btn btn-primary']) !!}
            </div>
            {!! Form::close() !!}
          </div>
        </div>
      </div>


@endsection
@section('css')
<link rel="stylesheet" href="{{ asset('assets/workplace/hound/vendors/datatables/media/css/jquery.dataTables.min.css')}}">
<link rel="stylesheet" href="{{ asset('assets/workplace/hound/vendors/nestable2/jquery.nestable.css')}}">
@endsection
@section('js')
<script src="{{ asset('assets/workplace/hound/vendors/datatables/media/js/jquery.dataTables.min.js')}}"></script>
<script src="{{ asset('assets/workplace/hound/vendors/nestable2/jquery.nestable.js')}}"></script>

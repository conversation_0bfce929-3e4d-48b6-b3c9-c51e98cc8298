!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=function(e){return function(){return e}},n=(t(!1),t(!0),function(e){for(var t=new Array(arguments.length-1),n=1;n<arguments.length;n++)t[n-1]=arguments[n];return function(){for(var n=new Array(arguments.length),a=0;a<n.length;a++)n[a]=arguments[a];var r=t.concat(n);return e.apply(null,r)}}),a=tinymce.util.Tools.resolve("tinymce.util.Tools"),r=tinymce.util.Tools.resolve("tinymce.util.XHR"),l=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),c=function(e){return e.getParam("template_cdate_classes","cdate")},o=function(e){return e.getParam("template_mdate_classes","mdate")},i=function(e){return e.getParam("template_selected_content_classes","selcontent")},s=function(e){return e.getParam("template_preview_replace_values")},u=function(e){return e.getParam("template_replace_values")},p=function(e){return e.templates},m=function(e){return e.getParam("template_cdate_format",e.getLang("template.cdate_format"))},d=function(e){return e.getParam("template_mdate_format",e.getLang("template.mdate_format"))},f=function(e){return e.getParam("template_popup_width",600)},g=function(e){return Math.min(l.DOM.getViewPort().h,e.getParam("template_popup_height",500))},h=function(e,t){if((e=""+e).length<t)for(var n=0;n<t-e.length;n++)e="0"+e;return e},y=function(e,t,n){var a="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),r="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),l="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),c="January February March April May June July August September October November December".split(" ");return n=n||new Date,t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace("%D","%m/%d/%Y")).replace("%r","%I:%M:%S %p")).replace("%Y",""+n.getFullYear())).replace("%y",""+n.getYear())).replace("%m",h(n.getMonth()+1,2))).replace("%d",h(n.getDate(),2))).replace("%H",""+h(n.getHours(),2))).replace("%M",""+h(n.getMinutes(),2))).replace("%S",""+h(n.getSeconds(),2))).replace("%I",""+((n.getHours()+11)%12+1))).replace("%p",n.getHours()<12?"AM":"PM")).replace("%B",""+e.translate(c[n.getMonth()]))).replace("%b",""+e.translate(l[n.getMonth()]))).replace("%A",""+e.translate(r[n.getDay()]))).replace("%a",""+e.translate(a[n.getDay()]))).replace("%%","%")},v=function(e,t,n){return a.each(n,function(e,n){"function"==typeof e&&(e=e(n)),t=t.replace(new RegExp("\\{\\$"+n+"\\}","g"),e)}),t},M=function(e,t){var n=e.dom,r=u(e);a.each(n.select("*",t),function(e){a.each(r,function(t,a){n.hasClass(e,a)&&"function"==typeof r[a]&&r[a](e)})})},_=function(e,t){return new RegExp("\\b"+t+"\\b","g").test(e.className)},b=function(e,t){return function(){var n=p(e);"function"!=typeof n?"string"==typeof n?r.send({url:n,success:function(e){t(JSON.parse(e))}}):t(n):n(t)}},T=v,x=M,P=function(e,t,n){var r,l,s=e.dom,p=e.selection.getContent();n=v(0,n,u(e)),r=s.create("div",null,n),(l=s.select(".mceTmpl",r))&&l.length>0&&(r=s.create("div",null)).appendChild(l[0].cloneNode(!0)),a.each(s.select("*",r),function(t){_(t,c(e).replace(/\s+/g,"|"))&&(t.innerHTML=y(e,m(e))),_(t,o(e).replace(/\s+/g,"|"))&&(t.innerHTML=y(e,d(e))),_(t,i(e).replace(/\s+/g,"|"))&&(t.innerHTML=p)}),M(e,r),e.execCommand("mceInsertContent",!1,r.innerHTML),e.addVisual()},S=function(e){e.addCommand("mceInsertTemplate",n(P,e))},w=function(e){e.on("PreProcess",function(t){var n=e.dom,r=d(e);a.each(n.select("div",t.node),function(t){n.hasClass(t,"mceTmpl")&&(a.each(n.select("*",t),function(t){n.hasClass(t,e.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(t.innerHTML=y(e,r))}),x(e,t))})})},D=function(e,t,n){if(-1===n.indexOf("<html>")){var r="";a.each(e.contentCSS,function(t){r+='<link type="text/css" rel="stylesheet" href="'+e.documentBaseURI.toAbsolute(t)+'">'});var l=e.settings.body_class||"";-1!==l.indexOf("=")&&(l=(l=e.getParam("body_class","","hash"))[e.id]||""),n="<!DOCTYPE html><html><head>"+r+'</head><body class="'+l+'">'+n+"</body></html>"}n=T(e,n,s(e));var c=t.find("iframe")[0].getEl().contentWindow.document;c.open(),c.write(n),c.close()},H=function(e,t){var n,l,c=[];if(t&&0!==t.length)a.each(t,function(e){c.push({selected:!c.length,text:e.title,value:{url:e.url,content:e.content,description:e.description}})}),(n=e.windowManager.open({title:"Insert template",layout:"flex",direction:"column",align:"stretch",padding:15,spacing:10,items:[{type:"form",flex:0,padding:0,items:[{type:"container",label:"Templates",items:{type:"listbox",label:"Templates",name:"template",values:c,onselect:function(t){var a=t.control.value();a.url?r.send({url:a.url,success:function(t){D(e,n,l=t)}}):(l=a.content,D(e,n,l)),n.find("#description")[0].text(t.control.value().description)}}}]},{type:"label",name:"description",label:"Description",text:"\xa0"},{type:"iframe",flex:1,border:1}],onsubmit:function(){P(e,!1,l)},minWidth:f(e),minHeight:g(e)})).find("listbox")[0].fire("select");else{var o=e.translate("No templates defined.");e.notificationManager.open({text:o,type:"info"})}},C=function(e){return function(t){H(e,t)}},A=function(e){e.addButton("template",{title:"Insert template",onclick:b(e.settings,C(e))}),e.addMenuItem("template",{text:"Template",onclick:b(e.settings,C(e)),icon:"template",context:"insert"})};e.add("template",function(e){A(e),S(e),w(e)})}();
<?php

namespace Modules\General\Http\Controllers;


use App\Admission;
use App\Center;
use App\CenterEmployee;
use App\Classes;
use App\Form;
use App\Scopes\OrganizationScope;
use App\Student;
use App\StudentHefzPlan;
use Carbon\Carbon;
use Illuminate\Http\Request;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;


class ShowArchivedhStudentsDatatablesController extends Controller
{

    public function __invoke(Request $request)
    {

        if ($request->ajax()) {


            $studentsQuery = Student::onlyTrashed()->orderBy('full_name')->select();


            return \Yajra\DataTables\DataTables::of($studentsQuery)

                ->addColumn('studentName', function ($row) {

                    return $row->full_name;

                }) ->addColumn('email', function ($row) {

                    return $row->email;

                })
                ->addColumn('center', function ($row) {

                    return $row->current_class->center->name;

                })

               ->addColumn('lastAdmissionStatus', function ($row) {
                   return $row->current_admission ? ( ($row->current_hefz_plan && $row->current_hefz_plan->status != 'active') ? $row->current_hefz_plan->status : $row->current_admission->status ): 'not registered';
                })
                ->addColumn('deletedDate', function ($row) {


                    return $row->deleted_at;


                })
                ->addColumn('deletedReason', function ($row) {


                    return $row->delete_reason;


                })
                ->addColumn('id',function ($row){

                    return $row->id;
                })
                ->addColumn('action', function ($row) use($request) {


                    // this condition is for ajax calls from #archivedStudentsDatatable table from resources/views/modules/education/classes/show.blade.php
                    if($request->has('existing_class_id') && $request->filled('existing_class_id'))
                    {
                        $btns = '
                      <div class="row">
                                                   
                                                    <div class="col-md-4">

                                                        <button type="button"
                                                        id="archivedStudentModalTriggerBtn"
                                                                class="btn btn-primary btn-xs dropdown-toggle"
                                                                data-target="#archivedStudentModal"
                                                                data-student_id="'.$row->id.'"
                                                                data-toggle="modal" aria-haspopup="true"
                                                                aria-expanded="false">
                                                            Add Student
                                                        </button>
                                                    </div>
                                                </div>
                                                                                                                ';

                    }
                    else{
                        $btns = '
                      <div class="row">
                                                    <div class="col-md-4">

                                                        <a href="'. route('admission.students.archive.show', $row->id).'"
                                                           class="btn btn-success btn-xs"
                                                           title="View Student">Restore</a>

                                                    </div>
                                                    <div class="col-md-4">

                                                        <button type="button"
                                                                class="btn btn-primary btn-xs dropdown-toggle"
                                                                data-target="#newStudentModal"
                                                                data-student_id="'.$row->id.'"
                                                                data-toggle="modal" aria-haspopup="true"
                                                                aria-expanded="false">
                                                            Add Student
                                                        </button>
                                                    </div>
                                                </div>
                                                                                                                ';

                    }


//
                    return $btns;
                })->rawColumns(['lastAdmissionStatus', 'action'])

                ->make(true);
        }


    }

    public function getHefzMonthlyPlanDetails(Request $request)
    {


        if ($request->ajax()) {
            $statusColumn = 'student_hefz_plans.status';
            $hefzTable = ',student_hefz_plans';
            $hefzTableJoinQuery = ' AND students.id = student_hefz_plans.student_id';
            $queryPart = ' AND student_hefz_plans.status = "waiting_for_approval"';
            $planWaitingApprovalStatus_condition = $queryPart;
            $archivedStatus = ' and students.deleted_at is null';


//            $application_need_action = auth()->user()->hasRole(
//                ["curriculum-specialist_2_","programs-specialist_2_","managing-director_2_","it-officer_2_","education-manager_2_"])

            $studentHefzMonthlyPlanDetails = StudentHefzPlan::where('id', $request->get('hefz_plans_id'))->select();


            return \Yajra\DataTables\DataTables::of($studentHefzMonthlyPlanDetails)
                ->addColumn('teacherName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('action', function ($row) {

                    $approveRoute = route('students.edit', $row->student->id);

                    $btns = ' <a href="' . $approveRoute . '" 
                                                                               class="btn btn-success btn-xs" title="Approve Plan"><span
                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                                                                 ';


//
                    return $btns;
                })->rawColumns(['action'])
                ->toJson();

        }


    }

    public function getApplicationsWaitingApprovals(Request $request)
    {


        if ($request->ajax()) {

//            $StudentsTableStatusqueryPart = ' AND students.status != "active" AND students.status != "suspended" AND students.status !="graduated" AND students.status !="rejected"';
            $StudentsTableStatusqueryPart = ' ';

            $centerFilter = auth()->user()->hasRole(
                ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "education-manager_2_", "administrative_2_"]) ? '' :
                "AND centers.id in ( select cen_id from cen_emps where emp_id = '" . \Auth::user()->id . "') ";


            auth()->user()->hasRole(
                ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "education-manager_2_"]) ?

                $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                    ->whereRaw('admissions.status = "new_admission"')
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select()
                :

                $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                    ->whereRaw('admissions.status = "new_admission"')
                    ->whereIn('center_id', CenterEmployee::where('emp_id', auth()->user()->id)->pluck('cen_id')->toArray())
                    ->has('programs')
                    ->with('programs')
                    ->has('center')
                    ->with('center')
                    ->has('student')
                    ->with('student')
                    ->withoutGlobalScope(OrganizationScope::class)
                    ->select();


            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('programTitle', function ($row) {
                    return $row->programs->map(function ($program) {
                        return $program->title;
                    })->implode('<br>');
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->addColumn('action', function ($row) {


                    $stEditRoute = route('students.edit', $row->student_id);
                    $stShowRoute = route('students.show', $row->student_id);

                    $btns = ' <a target="_blank" href="' . $stShowRoute . '" 
                                                                               class="btn btn-success btn-xs" title="View Center"><span
                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                                    ';
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Update Student details">
//                                                                             <span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>

//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//


//
                    return $btns;
                })
                ->rawColumns(['action'])
                ->toJson();


//            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
//                ->addColumn('action', function ($row) {
//
//                    $stEditRoute = route('admission.students.edit', $row->id);
//                    $stShowRoute = route('admission.students.show', $row->id);
//
//                    $btns = ' <a href="' . $stShowRoute . '"
//                                                                               class="btn btn-success btn-xs" title="View Center"><span
//                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
//
//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//                                                                                                                 ';
//
//
////
//                    return $btns;
//                })
//                ->rawColumns(['action'])
//                ->make(true);

        }
    }

    public function getMissedClockOuts(Request $request)
    {


        if ($request->ajax()) {

//            $StudentsTableStatusqueryPart = ' AND students.status != "active" AND students.status != "suspended" AND students.status !="graduated" AND students.status !="rejected"';
            $StudentsTableStatusqueryPart = ' ';

            $centerFilter = auth()->user()->hasRole(
                ["update-employee-clockout-details_2_"]) ? '' :

                $application_need_action = auth()->user()->hasRole(
                    ["curriculum-specialist_2_", "programs-specialist_2_", "managing-director_2_", "it-officer_2_", "education-manager_2_", "administrative_2_"]) ?

                    $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                        ->whereRaw('admissions.status = "new_admission"')
                        ->has('programs')
                        ->with('programs')
                        ->has('center')
                        ->with('center')
                        ->has('student')
                        ->with('student')
                        ->withoutGlobalScope(OrganizationScope::class)
                        ->select()
                    :

                    $studentsApprovalQuery = Admission::whereRaw('admissions.organization_id = ' . config('organization_id'))
                        ->whereRaw('admissions.status = "new_admission"')
                        ->whereIn('center_id', CenterEmployee::where('emp_id', auth()->user()->id)->pluck('cen_id')->toArray())
                        ->has('programs')
                        ->with('programs')
                        ->has('center')
                        ->with('center')
                        ->has('student')
                        ->with('student')
                        ->withoutGlobalScope(OrganizationScope::class)
                        ->select();


            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
                ->addColumn('programTitle', function ($row) {
                    return $row->programs->map(function ($program) {
                        return $program->title;
                    })->implode('<br>');
                })
                ->addColumn('centerName', function ($row) {
                    return $row->center->name;

                })
                ->addColumn('created_at', function ($row) {

                    return value($row['created_at'])->diffForHumans();
                })
                ->addColumn('action', function ($row) {


                    $stEditRoute = route('students.edit', $row->student_id);
                    $stShowRoute = route('students.show', $row->student_id);

                    $btns = ' <a href="' . $stShowRoute . '" 
                                                                               class="btn btn-success btn-xs" title="View Center"><span
                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
                                                                                    ';
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Update Student details">
//                                                                             <span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>

//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//


//
                    return $btns;
                })
                ->rawColumns(['action'])
                ->toJson();


//            return \Yajra\DataTables\DataTables::of($studentsApprovalQuery)
//                ->addColumn('action', function ($row) {
//
//                    $stEditRoute = route('admission.students.edit', $row->id);
//                    $stShowRoute = route('admission.students.show', $row->id);
//
//                    $btns = ' <a href="' . $stShowRoute . '"
//                                                                               class="btn btn-success btn-xs" title="View Center"><span
//                                                                                    class="glyphicon glyphicon-eye-open" aria-hidden="true"/></a>
//                                                                             <a href="' . $stEditRoute . '" class="btn btn-primary btn-xs" title="Edit Center"><span class="glyphicon glyphicon-pencil" aria-hidden="true"/></a>
//
//                                                                            <button class="btn btn-danger btn-xs deleteModalTriggerBtn " id="deleteModalTriggerBtn "
//                                                                                    data-catid=' . $row->id . ' data-toggle="modal"
//                                                                                    data-target="#delete"><span class="glyphicon glyphicon-trash"
//                                                                                                                title="Delete '.$row->full_name.'"/></button>
//                                                                                                                 ';
//
//
////
//                    return $btns;
//                })
//                ->rawColumns(['action'])
//                ->make(true);

        }
    }
}


//            }







<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

/**
 * System Health History Entity
 * 
 * Stores historical health check data for trending and analysis
 */
final class SystemHealthHistory extends Model
{
    use HasFactory;

    protected $table = 'system_health_history';

    protected $fillable = [
        'check_name',
        'status',
        'message',
        'metrics',
        'execution_time_ms',
        'checked_at',
    ];

    protected $casts = [
        'metrics' => 'array',
        'checked_at' => 'datetime',
    ];

    public $timestamps = false; // We use checked_at instead

    /**
     * Get the health check this history belongs to
     */
    public function healthCheck()
    {
        return $this->belongsTo(SystemHealthCheck::class, 'check_name', 'check_name');
    }

    /**
     * Scope for recent history
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('checked_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for specific check
     */
    public function scopeForCheck($query, string $checkName)
    {
        return $query->where('check_name', $checkName);
    }

    /**
     * Scope for specific status
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Get health trends for a specific check
     */
    public static function getTrends(string $checkName, int $hours = 24): array
    {
        $history = static::forCheck($checkName)
            ->where('checked_at', '>=', now()->subHours($hours))
            ->orderBy('checked_at')
            ->get();

        if ($history->isEmpty()) {
            return [
                'total_checks' => 0,
                'healthy_percentage' => 0,
                'average_execution_time' => 0,
                'trend' => 'unknown',
                'data_points' => [],
            ];
        }

        $totalChecks = $history->count();
        $healthyChecks = $history->where('status', SystemHealthCheck::STATUS_HEALTHY)->count();
        $healthyPercentage = ($healthyChecks / $totalChecks) * 100;
        
        $executionTimes = $history->whereNotNull('execution_time_ms')->pluck('execution_time_ms');
        $averageExecutionTime = $executionTimes->isEmpty() ? 0 : $executionTimes->average();

        // Determine trend (improving, degrading, stable)
        $recentHalf = $history->slice(-ceil($totalChecks / 2));
        $recentHealthy = $recentHalf->where('status', SystemHealthCheck::STATUS_HEALTHY)->count();
        $recentHealthyPercentage = ($recentHealthy / $recentHalf->count()) * 100;

        $trend = 'stable';
        if ($recentHealthyPercentage > $healthyPercentage + 10) {
            $trend = 'improving';
        } elseif ($recentHealthyPercentage < $healthyPercentage - 10) {
            $trend = 'degrading';
        }

        return [
            'total_checks' => $totalChecks,
            'healthy_percentage' => round($healthyPercentage, 2),
            'average_execution_time' => round($averageExecutionTime, 2),
            'trend' => $trend,
            'data_points' => $history->map(function ($item) {
                return [
                    'timestamp' => $item->checked_at->toISOString(),
                    'status' => $item->status,
                    'execution_time' => $item->execution_time_ms,
                ];
            })->toArray(),
        ];
    }

    /**
     * Get system-wide health trends
     */
    public static function getSystemTrends(int $hours = 24): array
    {
        $checks = SystemHealthCheck::pluck('check_name');
        $trends = [];

        foreach ($checks as $checkName) {
            $trends[$checkName] = static::getTrends($checkName, $hours);
        }

        // Calculate overall system health
        $totalChecks = collect($trends)->sum('total_checks');
        $overallHealthy = collect($trends)->sum(function ($trend) {
            return ($trend['healthy_percentage'] / 100) * $trend['total_checks'];
        });

        $overallHealthyPercentage = $totalChecks > 0 ? ($overallHealthy / $totalChecks) * 100 : 0;

        return [
            'overall_health_percentage' => round($overallHealthyPercentage, 2),
            'total_system_checks' => $totalChecks,
            'check_trends' => $trends,
            'critical_issues' => static::getCriticalIssues($hours),
        ];
    }

    /**
     * Get critical issues from recent history
     */
    public static function getCriticalIssues(int $hours = 24): array
    {
        return static::recent($hours)
            ->withStatus(SystemHealthCheck::STATUS_CRITICAL)
            ->with('healthCheck')
            ->get()
            ->groupBy('check_name')
            ->map(function ($issues, $checkName) {
                $latestIssue = $issues->sortByDesc('checked_at')->first();
                return [
                    'check_name' => $checkName,
                    'check_type' => $latestIssue->healthCheck->check_type ?? 'unknown',
                    'latest_message' => $latestIssue->message,
                    'first_occurred' => $issues->min('checked_at'),
                    'last_occurred' => $issues->max('checked_at'),
                    'occurrence_count' => $issues->count(),
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * Clean up old history records
     */
    public static function cleanup(int $daysToKeep = 30): int
    {
        return static::where('checked_at', '<', now()->subDays($daysToKeep))->delete();
    }
}

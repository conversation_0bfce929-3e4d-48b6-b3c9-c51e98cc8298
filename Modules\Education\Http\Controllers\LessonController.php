<?php

namespace Modules\Education\Http\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use App\Employee;
use App\Center;
use App\Cen_Emp;
use App\ProgramLevel;
use App\ProgramLevelLesson;
use App\Student;
use App\Talaqqi;
use App\Talqeen;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Session;

class LessonController extends Controller
{



    public function getToLessons(Request $request, $studentId)
    {
        $request->validate([
            'fromLessonId' => 'required|integer',
            'program_level_id' => 'required|integer|exists:program_levels,id'
        ]);

        try {
            $fromLessonId = $request->get('fromLessonId');
            $programLevelId = $request->get('program_level_id');
            $programLevelTitle = $request->get('program_level_title');

            $availableToLessons = [];

            if ($request->filled('program_level_title') && ($programLevelTitle == 'Level 1' || $programLevelTitle == 'Level 2')) {
                // Fetch the lessons for the specified program level and filter them
                $availableToLessons = ProgramLevel::findOrFail($programLevelId)->lessons()
                    ->get(['id', 'properties']) // Fetch 'id' and 'properties'
                    ->filter(function ($lesson) use ($fromLessonId) {
                        $properties = $lesson->properties; // No need for json_decode
                        return $lesson->id >= $fromLessonId;
                    })
                    ->map(function ($lesson) {
                        $properties = $lesson->properties; // No need for json_decode
                        return [
                            'id' => $lesson->id,
                            'name' => $properties['name'] ?? '',
                            'lesson_no' => $properties['no'] ?? '',
                            'letter' => $properties['letter'] ?? '',
                        ];
                    });
            } else {
                // If not Level 1 or Level 2, fetch lessons without specific filtering
                $availableToLessons = ProgramLevel::findOrFail($programLevelId)->lessons()
                    ->get(['id', 'properties'])
                    ->map(function ($lesson) {
                        $properties = $lesson->properties;
                        return [
                            'id' => $lesson->id,
                            'name' => $properties['name'] ?? '',
                            'lesson_no' => $properties['no'] ?? '',
                            'letter' => $properties['letter'] ?? '',
                        ];
                    });
            }

            return response()->json(['success' => true, 'availableToLessons' => $availableToLessons->values()]);
        } catch (\Exception $e) {
            \Log::error("Failed to fetch to lessons: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to fetch lessons.'], 500);
        }
    }
    public function dailyNouranyaReportgetToLessons(Request $request,  $studentId)
    {
        $request->validate([
            'fromLessonId' => 'required|integer',
            'program_level_id' => 'required|integer|exists:program_levels,id'
        ]);

        try {

            $student = Student::find($studentId);
            $fromLessonId = $request->get('fromLessonId');
            $programLevelId = $request->get('program_level_id');
            $programLevelTitle = $request->get('program_level_title');
            $toLessonId = optional($student->nouranya_plans->first())->to_lesson; // From Nouranya plan


            $availableToLessons = [];

            if ($request->filled('program_level_title') && ($programLevelTitle == 'Level 1' || $programLevelTitle == 'Level 2')) {
                // Fetch the lessons for the specified program level and filter them
                $availableToLessons = ProgramLevel::findOrFail($programLevelId)
                    ->lessons()
                    ->get(['id', 'properties']) // Fetch 'id' and 'properties'
                    ->filter(function ($lesson) use ($fromLessonId, $toLessonId) {
                        $properties = $lesson->properties;
                        $lessonNo = $properties['no'] ?? null; // Assuming 'no' holds the lesson number

                        // Filter lessons within the range of fromLessonId to toLessonId
                        return $lessonNo >= $fromLessonId && $lessonNo <= $toLessonId;
                    })
                    ->map(function ($lesson) {
                        $properties = $lesson->properties;
                        return [
                            'id' => $lesson->id,
                            'name' => $properties['name'] ?? '',
                            'lesson_no' => $properties['no'] ?? '',
                            'letter' => $properties['letter'] ?? '',
                        ];
                    });

            } else {
                // If not Level 1 or Level 2, fetch lessons without specific filtering
                $availableToLessons = ProgramLevel::findOrFail($programLevelId)->lessons()
                    ->get(['id', 'properties'])
                    ->map(function ($lesson) {
                        $properties = $lesson->properties;
                        return [
                            'id' => $lesson->id,
                            'name' => $properties['name'] ?? '',
                            'lesson_no' => $properties['no'] ?? '',
                            'letter' => $properties['letter'] ?? '',
                        ];
                    });
            }

            return response()->json(['success' => true, 'availableToLessons' => $availableToLessons->values()]);
        } catch (\Exception $e) {
            \Log::error("Failed to fetch to lessons: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to fetch lessons.'], 500);
        }
    }
    public function dailyIjazasanadReportgetTalqeenToLessons(Request $request,  $studentId)
    {
        $request->validate([
            'fromLessonId' => 'required|integer',
            'program_level_id' => 'required|integer|exists:program_levels,id'
        ]);


        try {

            $student = Student::find($studentId);
            $fromLessonId = $request->get('fromLessonId');
            $programLevelId = $request->get('program_level_id');
            $programLevelTitle = $request->get('program_level_title');
            $toLessonId = optional($student->ijazasanad_memorization_plans->first())->talqeen_to_lesson; // From Nouranya plan

            $availableToLessons = \App\ProgramLevelLesson::whereBetween('id', [(int)$fromLessonId, (int)$toLessonId])
                ->whereJsonContains('properties->course', 'ijazasanadtalqeen') // Restrict to course 'ijazasanadtalqeen'
                ->orderBy(DB::raw('CAST(JSON_UNQUOTE(JSON_EXTRACT(properties, "$.no")) AS UNSIGNED)'), 'asc') // Order by "no" key in properties JSON
                ->get();


            return response()->json(['success' => true, 'availableToLessons' => $availableToLessons->values()]);
        } catch (\Exception $e) {
            \Log::error("Failed to fetch to lessons: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to fetch lessons.'], 500);
        }
    }
    public function dailyIjazasanadReportgetLevel1RevisionToLessons(Request $request,  $studentId)
    {
        $request->validate([
            'fromLessonId' => 'required|integer',
            'program_level_id' => 'required|integer|exists:program_levels,id'
        ]);

        try {

            $student = Student::find($studentId);
            $fromLessonId = $request->get('fromLessonId');
            $programLevelId = $request->get('program_level_id');
            $programLevelTitle = $request->get('program_level_title');
            $toLessonId = optional($student->ijazasanad_memorization_plans->first())->revision_to_lesson; // From Nouranya plan

                // Fetch the lessons for the specified program level and filter them
                $availableToLessons =  \App\ProgramLevelLesson::where('id', '>=', (int)$fromLessonId)
                ->whereJsonContains('properties->course', 'ijazasanadrevision')
                ->orderBy(DB::raw('CAST(JSON_UNQUOTE(JSON_EXTRACT(properties, "$.no")) AS UNSIGNED)'), 'asc') // Order by "no" key in properties JSON
                ->get();




            return response()->json(['success' => true, 'availableToLessons' => $availableToLessons->values()]);
        } catch (\Exception $e) {
            \Log::error("Failed to fetch to lessons: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to fetch lessons.'], 500);
        }
    }
    public function dailyIjazasanadReportgetJazariyahToLessons(Request $request,  $studentId)
    {
        $request->validate([
            'fromLessonId' => 'required|integer',
            'program_level_id' => 'required|integer|exists:program_levels,id'
        ]);

        try {

            $student = Student::find($studentId);
            $fromLessonId = $request->get('fromLessonId');
            $programLevelId = $request->get('program_level_id');
            $programLevelTitle = $request->get('program_level_title');
            $toLessonId = optional($student->ijazasanad_memorization_plans->first())->jazariyah_to_lesson; // From Nouranya plan


            $availableToLessons = [];

                // Fetch the lessons for the specified program level and filter them
//                $availableToLessons = \App\ProgramLevelLesson::where('id', '>=', (int)$fromLessonId)
                $availableToLessons = \App\ProgramLevelLesson::whereBetween('id', [(int)$fromLessonId, (int)$toLessonId])
                ->whereJsonContains('properties->course', 'ijazasanadjazariyah') // Restrict to course 'ijazasanadtalqeen'
                ->orderBy(DB::raw('CAST(JSON_UNQUOTE(JSON_EXTRACT(properties, "$.no")) AS UNSIGNED)'), 'asc') // Order by "no" key in properties JSON
                ->get();


            return response()->json(['success' => true, 'availableToLessons' => $availableToLessons->values()]);
        } catch (\Exception $e) {
            \Log::error("Failed to fetch to lessons: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to fetch lessons.'], 500);
        }
    }
    public function dailyIjazasanadReportgetSeminarsToLessons(Request $request,  $studentId)
    {
        $request->validate([
            'fromLessonId' => 'required|integer',
            'program_level_id' => 'required|integer|exists:program_levels,id'
        ]);

        try {

            $student = Student::find($studentId);
            $fromLessonId = $request->get('fromLessonId');
            $programLevelId = $request->get('program_level_id');
            $programLevelTitle = $request->get('program_level_title');
            $toLessonId = optional($student->ijazasanad_memorization_plans->first())->seminars_to_lesson; // From Nouranya plan



                // Fetch the lessons for the specified program level and filter them
                $availableToLessons = \App\ProgramLevelLesson::where('id', '>=', (int)$fromLessonId)
                ->whereJsonContains('properties->course', 'ijazasanadseminars') // Restrict to course 'ijazasanadtalqeen'
                ->orderBy(DB::raw('CAST(JSON_UNQUOTE(JSON_EXTRACT(properties, "$.no")) AS UNSIGNED)'), 'asc') // Order by "no" key in properties JSON
                ->get();


            return response()->json(['success' => true, 'availableToLessons' => $availableToLessons->values()]);
        } catch (\Exception $e) {
            \Log::error("Failed to fetch to lessons: {$e->getMessage()}");
            return response()->json(['success' => false, 'message' => 'Failed to fetch lessons.'], 500);
        }
    }


    public function updateLessonOrder(Request $request)
    {
        $lessons = $request->input('lessons');

        foreach ($lessons as $lesson) {
            ProgramLevelLesson::where('id', $lesson['id'])
                ->update(['order' => $lesson['order']]);
        }

    }

    public function updateLevelThreeTalaqqiLessonOrder(Request $request)
    {
        $sortedData = $request->input('sortedData');

        foreach ($sortedData as $data) {

            $record = Talaqqi::find($data['id']);
            if ($record) {
                $record->order = $data['order'];
                $record->save();
            }
        }
        return response()->json(['status' => 'success']);

    }

    public function updateLevelThreeTalqeenLessonOrder(Request $request)
    {
        $sortedData = $request->input('sortedData');

        foreach ($sortedData as $data) {


            $record = Talqeen::find($data['id']);
            if ($record) {
                $record->order = $data['order'];
                $record->save();
            }
        }
        return response()->json(['status' => 'success']);

    }



}

<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Entities;

use Illuminate\Database\Eloquent\Model;

final class ProviderSetting extends Model
{
    protected $table = 'jobseeker_provider_settings';

    protected $fillable = [
        'provider_id',
        'provider_name',
        'allow_non_english_default',
        'mixed_as_english',
        'max_rtl_ratio',
    ];

    protected $casts = [
        'provider_id' => 'integer',
        'allow_non_english_default' => 'boolean',
        'mixed_as_english' => 'boolean',
        'max_rtl_ratio' => 'float',
    ];

    public function provider()
    {
        return $this->belongsTo(\Modules\JobSeeker\Entities\JobProvider::class, 'provider_id');
    }

    /**
     * Lookup by provider slug/name. Prefers FK if resolvable.
     */
    public static function forProvider(string $providerSlugOrName): ?self
    {
        // Try by JobProvider slug → provider_id
        $provider = \Modules\JobSeeker\Entities\JobProvider::findBySlug($providerSlugOrName);
        if ($provider) {
            $byId = static::where('provider_id', $provider->id)->first();
            if ($byId) {
                return $byId;
            }
        }
        // Fallback by legacy provider_name string
        return static::where('provider_name', $providerSlugOrName)->first();
    }
}



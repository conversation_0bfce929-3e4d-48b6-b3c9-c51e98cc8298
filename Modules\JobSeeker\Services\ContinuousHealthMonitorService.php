<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Exception;
use Illuminate\Support\Facades\Log;
use Modules\JobSeeker\Entities\SystemHealthCheck;
use Modules\JobSeeker\Services\SystemErrorNotificationService;
use Mo<PERSON>les\JobSeeker\Services\HealthCheckers\HealthCheckerInterface;
use Mo<PERSON>les\JobSeeker\Services\HealthCheckers\EmailNotificationHealthChecker;
use Mo<PERSON>les\JobSeeker\Services\HealthCheckers\JobFetchingHealthChecker;
use Modules\JobSeeker\Services\HealthCheckers\DatabaseHealthChecker;
use Modules\JobSeeker\Services\HealthCheckers\ConfigurationHealthChecker;
use Modules\JobSeeker\Services\HealthCheckers\QueueHealthChecker;
use Modules\JobSeeker\Services\HealthCheckers\ApiResponseTimeHealthChecker;
use Modules\JobSeeker\Services\HealthCheckers\SystemResourceHealthChecker;
use Mo<PERSON><PERSON>\JobSeeker\Services\AutomatedRecoveryService;
use Carbon\Carbon;

/**
 * Continuous Health Monitor Service
 * 
 * Orchestrates continuous health monitoring of all system components.
 * Runs health checks, attempts automated recovery, and sends alerts.
 * 
 * This service prevents issues like the 46-day email notification outage
 * by detecting problems within 15 minutes and taking corrective action.
 */
final class ContinuousHealthMonitorService
{
    private SystemErrorNotificationService $errorNotificationService;
    private AutomatedRecoveryService $recoveryService;
    private array $healthCheckers = [];

    public function __construct(
        SystemErrorNotificationService $errorNotificationService,
        AutomatedRecoveryService $recoveryService
    ) {
        $this->errorNotificationService = $errorNotificationService;
        $this->recoveryService = $recoveryService;
        $this->initializeHealthCheckers();
    }

    /**
     * Initialize all health checkers
     */
    private function initializeHealthCheckers(): void
    {
        $this->healthCheckers = [
            'email_notifications' => app(EmailNotificationHealthChecker::class),
            'job_fetching_acbar' => app(JobFetchingHealthChecker::class, ['provider' => 'acbar']),
            'job_fetching_jobsaf' => app(JobFetchingHealthChecker::class, ['provider' => 'jobsaf']),
            'database_connectivity' => app(DatabaseHealthChecker::class),
            'configuration_validation' => app(ConfigurationHealthChecker::class),
            'queue_processing' => app(QueueHealthChecker::class),
            'api_response_times' => app(ApiResponseTimeHealthChecker::class),
            'disk_space' => app(SystemResourceHealthChecker::class, ['type' => 'disk']),
            'memory_usage' => app(SystemResourceHealthChecker::class, ['type' => 'memory']),
            'recent_job_activity' => app(JobFetchingHealthChecker::class, ['provider' => 'activity']),
        ];
    }

    /**
     * Run all due health checks
     */
    public function runDueHealthChecks(): array
    {
        $results = [
            'checks_run' => 0,
            'healthy' => 0,
            'warnings' => 0,
            'critical' => 0,
            'recoveries_attempted' => 0,
            'recoveries_successful' => 0,
            'alerts_sent' => 0,
        ];

        Log::info('ContinuousHealthMonitor: Starting health check cycle');

        // Get all due health checks, prioritizing critical ones
        $dueChecks = SystemHealthCheck::due()
            ->orderByRaw("FIELD(check_type, 'critical', 'important', 'informational')")
            ->orderBy('consecutive_failures', 'desc')
            ->get();

        if ($dueChecks->isEmpty()) {
            Log::info('ContinuousHealthMonitor: No health checks due');
            return $results;
        }

        foreach ($dueChecks as $healthCheck) {
            try {
                $result = $this->runHealthCheck($healthCheck);
                $results['checks_run']++;
                
                switch ($result['status']) {
                    case SystemHealthCheck::STATUS_HEALTHY:
                        $results['healthy']++;
                        break;
                    case SystemHealthCheck::STATUS_WARNING:
                        $results['warnings']++;
                        break;
                    case SystemHealthCheck::STATUS_CRITICAL:
                        $results['critical']++;
                        break;
                }

                // Attempt recovery for unhealthy checks
                if ($result['status'] !== SystemHealthCheck::STATUS_HEALTHY) {
                    $recoveryResult = $this->attemptRecovery($healthCheck, $result);
                    if ($recoveryResult['attempted']) {
                        $results['recoveries_attempted']++;
                        if ($recoveryResult['successful']) {
                            $results['recoveries_successful']++;
                        }
                    }

                    // Send alert if recovery failed or wasn't possible
                    if (!($recoveryResult['successful'] ?? false)) {
                        $this->sendHealthAlert($healthCheck, $result);
                        $results['alerts_sent']++;
                    }
                }

            } catch (Exception $e) {
                Log::error('ContinuousHealthMonitor: Error running health check', [
                    'check_name' => $healthCheck->check_name,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                // Mark check as critical due to execution error
                $healthCheck->updateStatus(
                    SystemHealthCheck::STATUS_CRITICAL,
                    'Health check execution failed: ' . $e->getMessage(),
                    ['error' => $e->getMessage()],
                    null
                );

                $results['critical']++;
            }
        }

        Log::info('ContinuousHealthMonitor: Health check cycle completed', $results);
        return $results;
    }

    /**
     * Run a specific health check
     */
    private function runHealthCheck(SystemHealthCheck $healthCheck): array
    {
        $checkName = $healthCheck->check_name;
        
        if (!isset($this->healthCheckers[$checkName])) {
            Log::warning("ContinuousHealthMonitor: No health checker found for {$checkName}");
            return [
                'status' => SystemHealthCheck::STATUS_UNKNOWN,
                'message' => 'No health checker configured',
                'metrics' => [],
                'execution_time_ms' => 0,
            ];
        }

        $startTime = microtime(true);
        
        try {
            $checker = $this->healthCheckers[$checkName];
            $result = $checker->check();
            
            $executionTime = (int) ((microtime(true) - $startTime) * 1000);
            
            // Update health check record
            $healthCheck->updateStatus(
                $result['status'],
                $result['message'],
                $result['metrics'] ?? [],
                $executionTime
            );

            Log::debug("ContinuousHealthMonitor: Health check completed", [
                'check_name' => $checkName,
                'status' => $result['status'],
                'execution_time_ms' => $executionTime,
            ]);

            return array_merge($result, ['execution_time_ms' => $executionTime]);

        } catch (Exception $e) {
            $executionTime = (int) ((microtime(true) - $startTime) * 1000);
            
            Log::error("ContinuousHealthMonitor: Health check failed", [
                'check_name' => $checkName,
                'error' => $e->getMessage(),
                'execution_time_ms' => $executionTime,
            ]);

            $result = [
                'status' => SystemHealthCheck::STATUS_CRITICAL,
                'message' => 'Health check execution failed: ' . $e->getMessage(),
                'metrics' => ['error' => $e->getMessage()],
                'execution_time_ms' => $executionTime,
            ];

            $healthCheck->updateStatus(
                $result['status'],
                $result['message'],
                $result['metrics'],
                $result['execution_time_ms']
            );

            return $result;
        }
    }

    /**
     * Attempt automated recovery for unhealthy checks
     */
    private function attemptRecovery(SystemHealthCheck $healthCheck, array $checkResult): array
    {
        try {
            $recoveryResult = $this->recoveryService->attemptRecovery(
                $healthCheck->check_name,
                $checkResult
            );

            $healthCheck->markRecoveryAttempted($recoveryResult['successful'] ?? false);

            Log::info('ContinuousHealthMonitor: Recovery attempted', [
                'check_name' => $healthCheck->check_name,
                'attempted' => $recoveryResult['attempted'],
                'successful' => $recoveryResult['successful'] ?? false,
                'message' => $recoveryResult['message'] ?? 'No message',
            ]);

            return $recoveryResult;

        } catch (Exception $e) {
            Log::error('ContinuousHealthMonitor: Recovery attempt failed', [
                'check_name' => $healthCheck->check_name,
                'error' => $e->getMessage(),
            ]);

            $healthCheck->markRecoveryAttempted(false);

            return [
                'attempted' => true,
                'successful' => false,
                'message' => 'Recovery attempt failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Send health alert for critical issues
     */
    private function sendHealthAlert(SystemHealthCheck $healthCheck, array $checkResult): void
    {
        // Only send alerts for critical issues or repeated warnings
        if ($checkResult['status'] === SystemHealthCheck::STATUS_CRITICAL || 
            ($checkResult['status'] === SystemHealthCheck::STATUS_WARNING && $healthCheck->consecutive_failures >= 3)) {
            
            $this->errorNotificationService->reportSystemError(
                'Health Monitor',
                "Health check '{$healthCheck->check_name}' is {$checkResult['status']}",
                [
                    'check_name' => $healthCheck->check_name,
                    'check_type' => $healthCheck->check_type,
                    'status' => $checkResult['status'],
                    'message' => $checkResult['message'],
                    'metrics' => $checkResult['metrics'] ?? [],
                    'consecutive_failures' => $healthCheck->consecutive_failures,
                    'last_healthy_at' => $healthCheck->last_healthy_at?->toDateTimeString(),
                    'recovery_attempted' => $healthCheck->recovery_attempted,
                    'recovery_successful' => $healthCheck->recovery_successful,
                ]
            );
        }
    }

    /**
     * Get current system health status
     */
    public function getSystemHealthStatus(): array
    {
        return SystemHealthCheck::getHealthSummary();
    }

    /**
     * Force run all health checks (for testing/manual execution)
     */
    public function runAllHealthChecks(): array
    {
        // Reset next_check_at for all checks to force execution
        SystemHealthCheck::query()->update(['next_check_at' => now()]);
        
        return $this->runDueHealthChecks();
    }

    /**
     * Get detailed health report
     */
    public function getDetailedHealthReport(): array
    {
        $checks = SystemHealthCheck::with('history')->get();
        $summary = SystemHealthCheck::getHealthSummary();
        
        return [
            'summary' => $summary,
            'checks' => $checks->map(function ($check) {
                return [
                    'name' => $check->check_name,
                    'type' => $check->check_type,
                    'status' => $check->status,
                    'message' => $check->message,
                    'metrics' => $check->metrics,
                    'last_checked' => $check->updated_at,
                    'last_healthy' => $check->last_healthy_at,
                    'consecutive_failures' => $check->consecutive_failures,
                    'is_overdue' => $check->isOverdue(),
                    'next_check' => $check->next_check_at,
                ];
            })->toArray(),
            'generated_at' => now()->toDateTimeString(),
        ];
    }
}

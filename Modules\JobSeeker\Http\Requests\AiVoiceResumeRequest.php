<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\File;
use Modules\JobSeeker\Entities\JobSeekerResume;

/**
 * AiVoiceResumeRequest validates voice-to-text resume uploads for AI tailoring.
 * 
 * Purpose: Handle both client-side transcription and server-side audio upload fallback.
 * Business rules: Either transcript text OR audio file; size/duration limits; format validation.
 * Security: Prevents abuse via file size limits; validates audio formats; enforces user limits.
 */
final class AiVoiceResumeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request
     */
    public function authorize(): bool
    {
        // User must be authenticated as job_seeker and have manage_resumes permission
        return auth('job_seeker')->check() && 
               auth('job_seeker')->user()->can('jobseeker.manage_resumes');
    }

    /**
     * Get the validation rules that apply to the request
     */
    public function rules(): array
    {
        return [
            'title' => [
                'required',
                'string',
                'min:3',
                'max:150',
                'regex:/^[a-zA-Z0-9\s\-_().]+$/',
            ],
            // Either transcript_text (client-side transcription) OR audio_file (server fallback)
            'transcript_text' => [
                'required_without:audio_file',
                'nullable',
                'string',
                'min:50', // Minimum meaningful transcript
                'max:25000', // Reasonable limit for voice transcripts
            ],
            'audio_file' => [
                'required_without:transcript_text',
                'nullable',
                'file',
                File::types(['webm', 'wav', 'mp3', 'ogg', 'm4a'])
                    ->max(10 * 1024) // 10MB in KB
                    ->rules(['mimes:webm,wav,mp3,ogg,m4a']),
            ],
            'duration_seconds' => [
                'nullable',
                'integer',
                'min:5', // At least 5 seconds
                'max:180', // Maximum 3 minutes
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Please provide a title for your voice resume.',
            'title.min' => 'Resume title must be at least 3 characters long.',
            'title.max' => 'Resume title cannot exceed 150 characters.',
            'title.regex' => 'Resume title contains invalid characters. Use only letters, numbers, and common punctuation.',
            'transcript_text.required_without' => 'Either transcript text or audio file must be provided.',
            'transcript_text.min' => 'Transcript must be at least 50 characters long to be meaningful.',
            'transcript_text.max' => 'Transcript is too long. Please keep it under 25,000 characters.',
            'audio_file.required_without' => 'Either audio file or transcript text must be provided.',
            'audio_file.file' => 'The uploaded item must be a valid audio file.',
            'audio_file.mimes' => 'Only WebM, WAV, MP3, OGG, and M4A audio files are allowed.',
            'audio_file.max' => 'Audio file size cannot exceed 10MB.',
            'duration_seconds.min' => 'Audio must be at least 5 seconds long.',
            'duration_seconds.max' => 'Audio cannot exceed 3 minutes (180 seconds).',
        ];
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        // Clean up transcript text if provided
        if ($this->has('transcript_text') && !empty($this->input('transcript_text'))) {
            $cleanTranscript = $this->input('transcript_text');
            
            // Remove excessive whitespace and normalize
            $cleanTranscript = preg_replace('/\s+/', ' ', $cleanTranscript);
            $cleanTranscript = trim($cleanTranscript);
            
            $this->merge([
                'transcript_text' => $cleanTranscript,
            ]);
        }
    }

    /**
     * Configure the validator instance
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if user has reached the 5-resume limit
            $currentCount = JobSeekerResume::countForJobSeeker(auth('job_seeker')->id());
            
            if ($currentCount >= 5) {
                $validator->errors()->add('transcript_text', 
                    'You can only store up to 5 resumes. Please delete an existing resume before adding a voice resume.');
            }

            // Validate that we have either transcript OR audio file, not both
            $hasTranscript = $this->has('transcript_text') && !empty($this->input('transcript_text'));
            $hasAudioFile = $this->hasFile('audio_file');

            if ($hasTranscript && $hasAudioFile) {
                $validator->errors()->add('transcript_text', 
                    'Please provide either transcript text or audio file, not both.');
            }

            if (!$hasTranscript && !$hasAudioFile) {
                $validator->errors()->add('transcript_text', 
                    'Either transcript text or audio file is required.');
            }

            // Additional audio file validation
            if ($hasAudioFile) {
                $audioFile = $this->file('audio_file');
                
                // Check for zero-byte files
                if ($audioFile->getSize() === 0) {
                    $validator->errors()->add('audio_file', 'Empty audio files are not allowed.');
                }

                // Validate duration if provided
                if ($this->has('duration_seconds')) {
                    $duration = (int) $this->input('duration_seconds');
                    $fileSize = $audioFile->getSize();
                    
                    // Rough estimate: if file is too small for declared duration, it might be invalid
                    $minExpectedSize = $duration * 1000; // Very rough estimate (1KB per second minimum)
                    if ($fileSize < $minExpectedSize) {
                        $validator->errors()->add('audio_file', 
                            'Audio file size seems too small for the declared duration.');
                    }
                }

                // MIME type validation for security
                $detectedMime = $audioFile->getMimeType();
                $allowedMimes = [
                    'audio/webm',
                    'audio/wav',
                    'audio/wave',
                    'audio/x-wav',
                    'audio/mpeg',
                    'audio/mp3',
                    'audio/ogg',
                    'audio/x-m4a',
                    'audio/mp4',
                ];
                
                if (!in_array($detectedMime, $allowedMimes)) {
                    $validator->errors()->add('audio_file', 
                        'Invalid audio format detected: ' . $detectedMime);
                }
            }

            // Validate transcript quality if provided
            if ($hasTranscript) {
                $transcript = $this->input('transcript_text');
                
                // Check for minimum word count
                $wordCount = str_word_count($transcript);
                if ($wordCount < 10) {
                    $validator->errors()->add('transcript_text', 
                        'Transcript is too short. Please provide a more complete description of your experience.');
                }

                // Check for suspicious patterns (repeated characters, etc.)
                if (preg_match('/(.)\1{10,}/', $transcript)) {
                    $validator->errors()->add('transcript_text', 
                        'Transcript contains suspicious repeated characters.');
                }

                // Check for potential transcription errors or gibberish
                $shortWords = array_filter(explode(' ', strtolower($transcript)), function($word) {
                    return strlen($word) <= 2;
                });
                
                $shortWordRatio = count($shortWords) / max($wordCount, 1);
                if ($shortWordRatio > 0.6) { // More than 60% very short words might indicate poor transcription
                    $validator->errors()->add('transcript_text', 
                        'Transcript quality seems poor. Please try recording again or check your microphone.');
                }
            }
        });
    }

    /**
     * Get custom attributes for error messages
     */
    public function attributes(): array
    {
        return [
            'title' => 'resume title',
            'transcript_text' => 'transcript',
            'audio_file' => 'audio file',
            'duration_seconds' => 'duration',
        ];
    }

    /**
     * Handle a failed validation attempt
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        // Log validation failures for monitoring
        \Illuminate\Support\Facades\Log::info('Voice resume validation failed', [
            'user_id' => auth('job_seeker')->id(),
            'errors' => $validator->errors()->toArray(),
            'has_transcript' => $this->has('transcript_text') && !empty($this->input('transcript_text')),
            'has_audio_file' => $this->hasFile('audio_file'),
            'transcript_length' => $this->has('transcript_text') ? strlen($this->input('transcript_text') ?? '') : 0,
            'audio_file_info' => $this->hasFile('audio_file') ? [
                'size' => $this->file('audio_file')->getSize(),
                'mime' => $this->file('audio_file')->getMimeType(),
                'original_name' => $this->file('audio_file')->getClientOriginalName(),
            ] : null,
            'duration_seconds' => $this->input('duration_seconds'),
        ]);

        parent::failedValidation($validator);
    }
}

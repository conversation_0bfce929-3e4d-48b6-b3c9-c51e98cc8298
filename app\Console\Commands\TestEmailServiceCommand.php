<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EmailService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class TestEmailServiceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-service
                            {--email= : Email address to send test to}
                            {--debug : Enable debug mode for detailed logging}
                            {--dry-run : Test the email service without actually sending emails}
                            {--mode= : Force email sending mode: local, production, or immediate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the EmailService with enhanced logging and debugging. Supports local, production, and immediate sending modes';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting EmailService test...');
        
        // Get email address
        $email = $this->option('email');
        if (empty($email)) {
            $email = $this->ask('Enter email address to send test to');
        }
        
        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email address provided');
            return Command::FAILURE;
        }
        
        // Get options
        $debugMode = $this->option('debug');
        $dryRun = $this->option('dry-run');
        $mode = $this->option('mode');
        
        // Validate mode option
        $validModes = ['local', 'production', 'immediate'];
        if ($mode && !in_array($mode, $validModes)) {
            $this->error('Invalid mode specified. Valid modes are: ' . implode(', ', $validModes));
            return Command::FAILURE;
        }
        
        // Display mode information
        if ($mode) {
            $this->info("Forcing email sending mode: {$mode}");
        } else {
            $currentEnv = app()->environment();
            $autoMode = in_array($currentEnv, ['local', 'dev', 'testing']) ? 'local' : 'production';
            $this->info("Using automatic mode based on environment '{$currentEnv}': {$autoMode}");
        }
        
        // Create EmailService instance
        $emailService = app(\App\Services\EmailService::class);
        
        // Enable debug mode if requested
        if ($debugMode) {
            $emailService->setDebugMode(true);
            $this->info('Debug mode enabled - verbose logging active');
        }
        
        // Create test email data
        $recipient = [
            'email' => $email,
            'name' => 'Test Recipient',
        ];
        
        // Set explicit sender information to prevent "Invalid address: (From)" error
        $sender = [
            'email' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
            'name' => env('MAIL_FROM_NAME', 'ITQAN Email Test')
        ];
        
        // Verify sender email is valid
        if (empty($sender['email']) || !filter_var($sender['email'], FILTER_VALIDATE_EMAIL)) {
            $this->warn('Invalid or missing sender email in configuration.');
            $sender['email'] = $this->ask('Please provide a valid sender email address');
            
            if (!filter_var($sender['email'], FILTER_VALIDATE_EMAIL)) {
                $this->error('Invalid sender email address provided');
                return Command::FAILURE;
            }
        }
        
        $modeText = $mode ?: 'auto';
        $subject = "EmailService Test - Mode: {$modeText} - " . ($dryRun ? 'DRY RUN' : 'ACTUAL SEND') . ' - ' . now()->format('Y-m-d H:i:s');
        
        // Prepare sample view data
        $viewData = [
            'title' => 'Email Service Test',
            'content' => 'This is a test email from the EmailService test command.',
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'is_dry_run' => $dryRun,
            'server' => gethostname(),
            'environment' => app()->environment(),
            'sending_mode' => $mode ?: 'automatic',
        ];
        
        $this->info('Sending ' . ($dryRun ? 'test (dry run)' : 'actual') . ' email to: ' . $email);
        $this->info('From: ' . $sender['email'] . ' (' . $sender['name'] . ')');
        $this->info('Subject: ' . $subject);
        $startTime = microtime(true);
        
        try {
            $result = null;
            
            // Choose sending method based on mode option
            if ($mode === 'immediate') {
                // Use sendNow method for immediate SMTP sending
                $this->info('Using immediate SMTP sending (sendNow method)...');
                if ($dryRun) {
                    $this->info('DRY RUN: Would send email immediately via SMTP');
                    $result = ['success' => true, 'message' => 'Dry run completed for immediate mode', 'correlation_id' => Str::uuid()->toString()];
                } else {
                    $success = $emailService->sendNow(
                        $email,
                        $subject,
                        'emails.test',
                        $viewData,
                        $recipient['name'],
                        $sender
                    );
                    $result = [
                        'success' => $success,
                        'message' => $success ? 'Email sent immediately via SMTP' : 'Failed to send email via SMTP',
                        'correlation_id' => Str::uuid()->toString()
                    ];
                }
            } elseif ($mode === 'local') {
                // Force local mode (synchronous via mail() function)
                $this->info('Using local mode (synchronous via mail() function)...');
                if ($dryRun) {
                    $this->info('DRY RUN: Would send email via mail() function');
                    $result = ['success' => true, 'message' => 'Dry run completed for local mode', 'correlation_id' => Str::uuid()->toString()];
                } else {
                    // Use reflection to call the private method for testing purposes
                    $reflection = new \ReflectionClass($emailService);
                    $method = $reflection->getMethod('_sendSynchronouslyViaMail');
                    $method->setAccessible(true);
                    $success = $method->invoke($emailService, $email, $subject, 'emails.test', $viewData, $recipient['name'], $sender, []);
                    $result = [
                        'success' => $success,
                        'message' => $success ? 'Email sent via local mode (mail() function)' : 'Failed to send email via local mode',
                        'correlation_id' => Str::uuid()->toString()
                    ];
                }
            } elseif ($mode === 'production') {
                // Force production mode (queue-based)
                $this->info('Using production mode (queue-based sending)...');
                if ($dryRun) {
                    $this->info('DRY RUN: Would dispatch email to queue');
                    $result = ['success' => true, 'message' => 'Dry run completed for production mode', 'correlation_id' => Str::uuid()->toString()];
                } else {
                    // Use reflection to call the private method for testing purposes
                    $reflection = new \ReflectionClass($emailService);
                    $method = $reflection->getMethod('_dispatchToQueue');
                    $method->setAccessible(true);
                    $success = $method->invoke($emailService, $email, $subject, 'emails.test', $viewData, $recipient['name'], $sender, [], [], null);
                    $result = [
                        'success' => $success,
                        'message' => $success ? 'Email dispatched to queue (production mode)' : 'Failed to dispatch email to queue',
                        'correlation_id' => Str::uuid()->toString()
                    ];
                }
            } else {
                // Use automatic mode detection (original behavior)
                $this->info('Using automatic mode detection...');
                $result = $emailService->sendEmail(
                    $recipient,
                    $subject,
                    'emails.test',
                    $viewData,
                    $sender,
                    [],
                    [],
                    null,
                    $dryRun
                );
            }
            
            $execTime = round((microtime(true) - $startTime) * 1000, 2);
            
            $isSuccess = is_array($result) ? ($result['success'] ?? false) : (is_bool($result) ? $result : false);

            if ($isSuccess) {
                $this->info('Email ' . ($dryRun ? 'dry run' : 'sending') . ' completed successfully!');
                $this->info("Process completed in {$execTime}ms");
                if(isset($result['correlation_id'])) {
                    $this->info("Correlation ID: {$result['correlation_id']}");
                }
                if(isset($result['message'])) {
                    $this->info("Result: {$result['message']}");
                }
                
                // Additional info for queue-based sending
                if ($mode === 'production' && !$dryRun) {
                    $this->info('Note: Email was dispatched to queue. Check queue worker logs for actual sending status.');
                    $this->info('You can monitor the queue with: php artisan queue:work --verbose');
                }
                
                if ($debugMode && isset($result['data'])) {
                    $this->info('Result data: ' . json_encode($result['data'], JSON_PRETTY_PRINT));
                }
                
                return Command::SUCCESS;
            } else {
                $this->error('Email sending failed: ' . $result['message']);
                if (isset($result['data']['error'])) {
                    $this->error("Error: " . $result['data']['error']);
                }
                
                if ($debugMode) {
                    $this->line('');
                    $this->line('Debug information:');
                    $this->line(json_encode($result, JSON_PRETTY_PRINT));
                } else {
                    $this->info('Run with --debug option for more information');
                }
                
                return Command::FAILURE;
            }
        } catch (\Exception $e) {
            $this->error('Exception occurred: ' . $e->getMessage());
            Log::error('TestEmailServiceCommand: Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Command::FAILURE;
        }
    }
} 
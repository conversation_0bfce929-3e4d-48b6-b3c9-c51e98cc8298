@startuml Authentication and Authorization Flow

!theme vibrant

actor User
participant Browser
participant "Laravel App" as App
participant "Auth Middleware" as AuthMiddleware
participant "Auth Manager" as AuthManager
participant "Guard (Session/Token)" as Guard
participant "Controller" as Controller
participant "Gate / Policy" as Authz

== Authentication (Login) ==

User -> Browser : Enters Credentials & Submits Login Form
Browser -> App : POST /login Request
activate App

App -> AuthMiddleware : Request through Middleware
activate AuthMiddleware
AuthMiddleware -> App : Continue Request
deactivate AuthMiddleware

App -> Controller : Route maps to LoginController@login
activate Controller
Controller -> AuthManager : attempt(credentials)
activate AuthManager
AuthManager -> Guard : attempt(credentials)
activate Guard
Guard -> Database : Verify Credentials
Database --> Guard : Credentials Valid / Invalid
Guard --> AuthManager : Authentication Success / Failure
deactivate Guard

alt Authentication Successful
    AuthManager -> Guard : login(User)
    activate Guard
    Guard -> Session : Store User ID in Session / Generate Token
    Guard --> AuthManager : User Logged In
    deactivate Guard
    AuthManager --> Controller : true
    Controller -> App : Redirect to Dashboard (Response)
else Authentication Failed
    AuthManager --> Controller : false
    Controller -> App : Redirect Back with Errors (Response)
deactivate AuthManager
end

App --> Browser : Sends Response (Redirect/Errors)
deactivate Controller
deactivate App
Browser --> User : Displays Dashboard or Login Form with Errors

== Authorization (Accessing Protected Resource) ==

User -> Browser : Clicks Link to /protected-resource
Browser -> App : GET /protected-resource Request (with Session Cookie/Token)
activate App

App -> AuthMiddleware : Request through Middleware ('auth')
activate AuthMiddleware
AuthMiddleware -> Guard : check()
activate Guard
Guard -> Session : Verify User ID in Session / Validate Token
Guard --> AuthMiddleware : Authenticated / Unauthenticated
deactivate Guard

alt Authenticated
    AuthMiddleware -> App : Continue Request
else Unauthenticated
    AuthMiddleware -> App : Redirect to Login (Response)
    App --> Browser : Sends Redirect Response
    deactivate AuthMiddleware
    deactivate App
    Browser --> User : Shows Login Page
    stop
end
deactivate AuthMiddleware

App -> Controller : Route maps to ProtectedController@index
activate Controller

' Authorization Check (Example: Using Gate)
Controller -> Authz : Gate::allows('view-resource', resource)
activate Authz
Authz -> Policy : Executes Policy logic / Gate Closure
Authz --> Controller : true (Authorized) / false (Unauthorized)
deactivate Authz

alt Authorized
    Controller -> App : Prepare Resource View (Response)
else Unauthorized (Forbidden)
    Controller -> App : Abort(403) (Response)
end

App --> Browser : Sends Response (Resource View / 403 Error)
deactivate Controller
deactivate App
Browser --> User : Displays Protected Resource or Error Page

@enduml 
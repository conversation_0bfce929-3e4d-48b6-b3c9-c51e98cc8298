<?php

declare(strict_types=1);

/*
|--------------------------------------------------------------------------
| Primary web domain
|--------------------------------------------------------------------------
|
| Several public routes are scoped with a domain constraint using
| config('domain'). If this value is null, those routes won't be
| registered and paths like "/en" will return 404. We derive the
| domain from APP_DOMAIN when available, otherwise from APP_URL.
*/

return env('APP_DOMAIN', 'itqan.test');
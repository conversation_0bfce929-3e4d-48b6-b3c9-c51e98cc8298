<?php

namespace Modules\Admission\Http\Controllers;

use App\EmailSetting;
use App\Employee;
use App\GeneralSettings;
use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Admission;
use App\AdmissionInterview;
use App\AdmissionInterviewReport;
use App\StudentHefzPlan;
use Brian2694\Toastr\Facades\Toastr;
use Carbon\Carbon;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Modules\Admission\Http\Requests\ConfirmInterviewRequest;
use Session;
use App\Student;
class AdmissionInterviewsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $keyword = $request->get('search');
        $perPage = 25;

        if (!empty($keyword)) {
            $admissioninterviews = AdmissionInterview::where('interview_time', 'LIKE', "%$keyword%")
				->orWhere('location', 'LIKE', "%$keyword%")
				->orWhere('status', 'LIKE', "%$keyword%")
				->paginate($perPage);
        } else {
            $admissioninterviews = AdmissionInterview::paginate($perPage);
        }

        return view('education.admission-interviews.index', compact('admissioninterviews'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('education.admission-interviews.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {

        return $request->all();
        
        $requestData = $request->all();
        
        AdmissionInterview::create($requestData);

        Session::flash('flash_message', 'AdmissionInterview added!');

        return redirect('workplace/education/admission-interviews');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $admissioninterview = AdmissionInterview::findOrFail($id);

        return view('education.admission-interviews.show', compact('admissioninterview'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $admissioninterview = AdmissionInterview::findOrFail($id);

        return view('education.admission-interviews.edit', compact('admissioninterview'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update($id, Request $request)
    {
        
        $requestData = $request->all();
        
        $admissioninterview = AdmissionInterview::findOrFail($id);
        $admissioninterview->update($requestData);

        Session::flash('flash_message', 'AdmissionInterview updated!');

        return redirect('workplace/education/admission-interviews');
    }

        /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function report(Request $request)
    {
        $interview_id = $request->interview['interview_id'];

        $admissioninterview = AdmissionInterview::findOrFail($interview_id);

        $admission = Admission::findorFail($admissioninterview->admission_id);
        
        if(! in_array(auth()->user()->id , $admissioninterview->interviewers->pluck('id')->toArray()) ){
            return "NOT AUTH";
        }
        
        $requestData = $request->all();

        $attachement = '';

        if($request->hasFile('attachement')){

            $imageName = \Illuminate\Support\Str::random(9) .
            $request->file('attachement')->getClientOriginalExtension();

            $path = 'userfiles/'.userfolder($id).'/'.\Illuminate\Support\Str::random(5).'_interview_report/';
            
            $request->file('image')->move(
                base_path() .'/public/'. $path, $imageName
            );
            $attachement = $path.$imageName;
        }
        
        $reportData = $requestData['interview'];

        // validate 
        
        // store to report table

        $report = new AdmissionInterviewReport;

        $report->admission_interview_id = $interview_id;

        $report->employee_id = auth()->user()->id;

        if($reportData['approve'] && isset($reportData['level'])){
            $report->program_level_id = $reportData['level'];
        }else{
            $report->program_level_id = 0;            
        }

        $report->attachement = $attachement;

        $report->notes = $reportData['notes'];


        // Intervewer attendence 

        foreach($admissioninterview->interviewers as $interviewer){
            if(isset($request->interviewer[$interviewer->id]) && $request->interviewer[$interviewer->id]['attended']){
                $admissioninterview->interviewers()->updateExistingPivot($interviewer->id , ['attended' => 1]);
            }else{
                $admissioninterview->interviewers()->updateExistingPivot($interviewer->id , ['attended' => 0]);                
            }
        }

        if(isset($admissioninterview->program->setting['special_program_code'])){
            if($admissioninterview->program->setting['special_program_code'] == 'hefz'){
                // print_r($request->hefz);
                // exit();
                $request->student_id = $admission->student_id;
//                app('Modules\Admission\Http\Controllers\AdmissionController')->createHefzPlan($request);
            }
        }

        // change interview status

        $admissioninterview->status = "interviewed";

        // change admission status
        if($reportData['approve']){
            $admission->status = "offered";            
        }else{
            $admission->status = "rejected";            
        }

        // change student status

        // save changes if no error
        $report->save();
        
        $admission->save();

        $admissioninterview->save();

        if($request->ajax()){
            return response()->json([
                "status" => "success"
            ]);
        }

        Session::flash('flash_message', 'AdmissionInterview updated!');

        return redirect()->back();

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        AdmissionInterview::destroy($id);

        Session::flash('flash_message', 'AdmissionInterview deleted!');

        return redirect('workplace/education/admission-interviews');
    }

    public function acceptInterview(ConfirmInterviewRequest $request, AdmissionInterview  $admissionInterview /** explicit route binding* check RouteServiceProvider */)
    {

        // update the admissionInterview with the details from the invite

        $admissionInterview->confirmed_at = Carbon::now();
        $admissionInterview->invitation_confirmed = 'confirmed';
        $admissionInterview->save();

        try {

            $admission = Admission::find($admissionInterview->admission_id);
//        send interview confirmation email to the guardian and student
        $systemSetting = GeneralSettings::select('organization_name', 'email')->find(1);

        $systemEmail = EmailSetting::find(1);

        $system_email = $systemEmail->from_email;
        $organization_name = $systemSetting->organization_name;

        $sender['system_email'] = $system_email;
        $sender['organization_name'] = $organization_name;

        if ($admission->creator_role == 'parent') {
            $user_info[0]["guardianEmail"] = $admission->guardian_email;
        }


        $user_info[0]["studentEmail"] = $admission->student_email;
        $user_info[0]["studentName"] = Student::find($admission->student_id)->full_name;
        $user_info[0]["interviewConfirmationTime"] = $admissionInterview->updated_at->format('m/d/Y H:i:s');


        dispatch(new \App\Jobs\SendStudentInterviewConfirmationToStudentMailJob($user_info, $sender));

        } catch (\Exception $e) {

            Toastr::warning($e->getMessage(), 'Warning');
            return redirect('student-list');
        }
            // here you would probably log the user in and show them the dashboard, but we'll just prove it worked
            Toastr::success('interview Confirmed. Please be on time for the interview as per the email sent to you', 'Success');

        if(\Auth::guard("web")->check()){

            if (\Auth::guard("web")->user()->hasRole("student")) {
                // return $next($request);
                return redirect('student-dashboard');
            }
            if(\Auth::guard("web")->user()->hasRole("parent")) {
                return redirect('parent-dashboard');
                return $next($request);
            }


            return redirect()->to("applicationcenter/application-list");
        }
        return redirect()->to("/login");
    }
}
